# Budget Basket

A Flutter app designed to help shoppers stay within their budget while shopping at their favorite retail stores.

## Features

- **Budget Setting**: Set your shopping budget before you start
- **Store Selection**: Choose from popular stores (BUDGET BASKET, MAKRO, WOOLWORTHS)
- **Barcode Scanning**: Scan product barcodes to get product details
- **Budget Tracking**: Real-time calculation of total cost against your budget
- **Shopping Cart**: Add, update, and remove items from your cart
- **Budget Alerts**: Visual indicators when you're approaching or exceeding your budget

## How It Works

1. **Start Shopping**: Launch the app and tap "Start Shopping"
2. **Set Budget**: Enter your shopping budget
3. **Select Store**: Choose the store you're shopping at
4. **Scan Products**: Use the camera to scan product barcodes or enter them manually
5. **Add to Cart**: Select products from search results to add to your cart
6. **Monitor Budget**: Keep track of your spending with real-time budget updates

## Technical Details

- Built with Flutter
- Uses Provider for state management
- Integrates with barcode scanning capabilities
- Connects to the Budget Basket API to fetch product details

## API Integration

The app uses the Budget Basket API for product searches and data:
```
Base URL: https://www.kemavuso.co.za/budget/
```

### Main Endpoints:
- `GET /products` - Fetch all products with pagination
- `GET /search` - Search products across stores
- `POST /search` - Search products by barcode

Example search payload:
```json
{
  "store": "all",
  "barcode": "6009510806885"
}
```

## Getting Started

To run this project:

1. Ensure you have Flutter installed on your machine
2. Clone the repository
3. Run `flutter pub get` to install dependencies
4. Connect a device or start an emulator
5. Run `flutter run` to start the app

## Dependencies

- flutter_barcode_scanner: For scanning product barcodes
- provider: For state management
- http: For API requests
- shared_preferences: For local storage
- intl: For formatting numbers and dates
