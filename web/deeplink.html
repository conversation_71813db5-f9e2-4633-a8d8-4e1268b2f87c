<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Budget Basket - Deep Links</title>
  <style>
    body {
      font-family: '<PERSON>to', <PERSON>l, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f9f9f9;
    }
    h1 {
      color: #2e7d32;
      text-align: center;
      margin-bottom: 30px;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    .button {
      display: inline-block;
      background-color: #2e7d32;
      color: white;
      padding: 12px 24px;
      border-radius: 4px;
      text-decoration: none;
      font-weight: bold;
      margin: 10px 5px;
      text-align: center;
    }
    .button:hover {
      background-color: #1b5e20;
    }
    code {
      background-color: #f5f5f5;
      padding: 2px 5px;
      border-radius: 3px;
      font-family: monospace;
    }
    .section {
      margin-bottom: 30px;
    }
    .note {
      background-color: #fff8e1;
      padding: 10px;
      border-left: 4px solid #ffc107;
      margin: 15px 0;
    }
  </style>
</head>
<body>
  <h1>Budget Basket Deep Links</h1>
  
  <div class="card section">
    <h2>Open Budget Basket App</h2>
    <p>Click the buttons below to open the Budget Basket app on your device:</p>
    
    <a href="budgetbasket://app/home" class="button">Open App (Home)</a>
    <a href="budgetbasket://app/products" class="button">Open Products</a>
    <a href="budgetbasket://app/cart" class="button">Open Cart</a>
    <a href="budgetbasket://app/profile" class="button">Open Profile</a>
    
    <div class="note">
      <p><strong>Note:</strong> If the app is not installed, these links may not work. In that case, you'll be redirected to download the app.</p>
    </div>
  </div>
  
  <div class="card section">
    <h2>Web Links (Universal/App Links)</h2>
    <p>These links work on both web browsers and mobile devices with the app installed:</p>
    
    <a href="https://budgetbasket.app/home" class="button">Open App via Web</a>
    <a href="https://budgetbasket.app/products?category=groceries" class="button">Grocery Products</a>
    <a href="https://budgetbasket.app/products?category=electronics" class="button">Electronics</a>
  </div>
  
  <div class="card section">
    <h2>Share Product</h2>
    <p>Share specific products with friends:</p>
    
    <a href="budgetbasket://app/product?id=12345&name=budget-coffee" class="button">Share Coffee</a>
    <a href="budgetbasket://app/product?id=67890&name=budget-milk" class="button">Share Milk</a>
  </div>
  
  <div class="card section">
    <h2>Developer Information</h2>
    <p>To implement deep links in your website or marketing materials, use one of these formats:</p>
    
    <h3>Custom URL Scheme (works on all platforms):</h3>
    <code>budgetbasket://app/[route]?[parameters]</code>
    
    <h3>Web URL (Universal/App Links):</h3>
    <code>https://budgetbasket.app/[route]?[parameters]</code>
    
    <h3>Example Routes:</h3>
    <ul>
      <li><code>/home</code> - Opens the home screen</li>
      <li><code>/products</code> - Opens the products list</li>
      <li><code>/product?id=12345</code> - Opens a specific product</li>
      <li><code>/cart</code> - Opens the shopping cart</li>
      <li><code>/profile</code> - Opens the user profile</li>
    </ul>
  </div>
  
  <div class="card">
    <p style="text-align: center;">
      <a href="index.html" class="button">Back to Home</a>
      <a href="https://budgetbasket.app/download" class="button">Download App</a>
    </p>
  </div>
</body>
</html>
