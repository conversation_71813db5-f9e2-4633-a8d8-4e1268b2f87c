<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Download Budget Basket</title>
  <style>
    body {
      font-family: '<PERSON>to', <PERSON>l, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f9f9f9;
      text-align: center;
    }
    h1 {
      color: #2e7d32;
      margin-bottom: 30px;
    }
    .logo {
      width: 120px;
      height: 120px;
      margin: 0 auto 20px;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 30px;
      margin-bottom: 20px;
    }
    .button {
      display: inline-block;
      background-color: #2e7d32;
      color: white;
      padding: 15px 30px;
      border-radius: 4px;
      text-decoration: none;
      font-weight: bold;
      margin: 10px;
      min-width: 200px;
    }
    .button:hover {
      background-color: #1b5e20;
    }
    .store-buttons {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 20px;
      margin: 30px 0;
    }
    .store-button {
      height: 60px;
    }
    .features {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 20px;
      margin: 30px 0;
      text-align: left;
    }
    .feature {
      flex: 1;
      min-width: 200px;
      padding: 15px;
    }
    .feature h3 {
      color: #2e7d32;
    }
    .feature-icon {
      font-size: 24px;
      margin-bottom: 10px;
      color: #2e7d32;
    }
  </style>
  <script>
    // Detect platform and redirect if needed
    window.onload = function() {
      // Check if the user was redirected from a deep link
      const urlParams = new URLSearchParams(window.location.search);
      const redirectPath = urlParams.get('redirect');
      
      // Store the redirect path for later use
      if (redirectPath) {
        localStorage.setItem('budgetBasketRedirect', redirectPath);
      }
      
      // Detect platform
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;
      
      // iOS detection
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        document.getElementById('ios-button').style.display = 'block';
      } 
      // Android detection
      else if (/android/i.test(userAgent)) {
        document.getElementById('android-button').style.display = 'block';
      }
      // Show both for desktop
      else {
        document.getElementById('ios-button').style.display = 'block';
        document.getElementById('android-button').style.display = 'block';
      }
    };
    
    // Function to try opening the app first, then fallback to store
    function tryOpenApp(storeUrl) {
      // Get any stored redirect path
      const redirectPath = localStorage.getItem('budgetBasketRedirect') || 'home';
      
      // Try to open the app with the deep link
      const appUrl = `budgetbasket://app/${redirectPath}`;
      
      // Create an iframe to try opening the app
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = appUrl;
      document.body.appendChild(iframe);
      
      // Set a timeout to redirect to the app store if the app doesn't open
      setTimeout(function() {
        window.location.href = storeUrl;
      }, 1000);
    }
  </script>
</head>
<body>
  <div class="card">
    <div class="logo">
      <svg width="120" height="120" viewBox="0 0 120 120">
        <circle cx="60" cy="60" r="60" fill="#4CAF50" />
        <path d="M40 45 L40 85 L80 85 L80 45 Z" fill="white" />
        <path d="M35 40 L85 40 L85 50 L35 50 Z" fill="white" />
        <path d="M50 30 L70 30 L70 40 L50 40 Z" fill="white" />
      </svg>
    </div>
    
    <h1>Download Budget Basket</h1>
    <p>Shop smart, stay within budget with our easy-to-use shopping app!</p>
    
    <div class="store-buttons">
      <a id="android-button" href="#" onclick="tryOpenApp('https://play.google.com/store/apps/details?id=za.co.budgetbasket')" style="display: none;">
        <img src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png" alt="Get it on Google Play" class="store-button">
      </a>
      
      <a id="ios-button" href="#" onclick="tryOpenApp('https://apps.apple.com/app/budgetbasket/id123456789')" style="display: none;">
        <img src="https://developer.apple.com/app-store/marketing/guidelines/images/badge-download-on-the-app-store.svg" alt="Download on the App Store" class="store-button">
      </a>
    </div>
  </div>
  
  <div class="card">
    <h2>Why Budget Basket?</h2>
    
    <div class="features">
      <div class="feature">
        <div class="feature-icon">💰</div>
        <h3>Save Money</h3>
        <p>Compare prices across stores and find the best deals to maximize your savings.</p>
      </div>
      
      <div class="feature">
        <div class="feature-icon">📋</div>
        <h3>Smart Lists</h3>
        <p>Create shopping lists that help you stick to your budget and avoid impulse purchases.</p>
      </div>
      
      <div class="feature">
        <div class="feature-icon">📊</div>
        <h3>Budget Tracking</h3>
        <p>Set spending limits and track your grocery expenses over time.</p>
      </div>
    </div>
    
    <a href="deeplink.html" class="button">Learn More About Deep Links</a>
  </div>
</body>
</html>
