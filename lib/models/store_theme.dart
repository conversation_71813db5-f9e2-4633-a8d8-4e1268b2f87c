import 'package:flutter/material.dart';

/// Model class that encapsulates branding information for each store
class StoreTheme {
  final String storeName;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;
  final String fontFamily;
  final IconData logo;
  final bool useDarkTextInDarkMode;

  const StoreTheme({
    required this.storeName,
    required this.primaryColor,
    required this.secondaryColor,
    this.textColor = Colors.black,
    this.fontFamily = 'Lato',
    required this.logo,
    this.useDarkTextInDarkMode = true,
  });

  // Default theme when no store is selected
  static final defaultTheme = StoreTheme(
    storeName: 'Default',
    primaryColor: Colors.green.shade700,
    secondaryColor: Colors.green.shade200,
    logo: Icons.shopping_basket,
  );

  /// Predefined store themes based on official branding
  static final Map<String, StoreTheme> storeThemes = {
    'Budget Basket': StoreTheme(
      storeName: 'Budget Basket',
      primaryColor: const Color(0xFF06C167), // Green
      secondaryColor: const Color(0xFF4ADE80), // Light Green
      textColor: Colors.white,
      logo: Icons.shopping_basket,
    ),
    'Woolworths': StoreTheme(
      storeName: 'Woolworths',
      primaryColor: Colors.black,
      secondaryColor: Colors.white,
      textColor: Colors.white,
      logo: Icons.shopping_bag,
      useDarkTextInDarkMode: false, // Keep text white in dark mode
    ),
    'Default': StoreTheme(
      storeName: 'Default',
      primaryColor: Colors.green.shade700,
      secondaryColor: Colors.green.shade200,
      logo: Icons.shopping_basket,
    ),
  };

  /// Get a store theme by name
  static StoreTheme? getThemeByName(String storeName) {
    return storeThemes[storeName];
  }
}
