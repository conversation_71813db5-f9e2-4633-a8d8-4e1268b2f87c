import '../models/grocery_product.dart';

class StoreProducts {
  final List<GroceryProduct> products;
  final int count;
  final bool hasNext;

  StoreProducts({
    required this.products,
    required this.count,
    this.hasNext = false,
  });

  factory StoreProducts.fromJson(Map<String, dynamic> json) {
    return StoreProducts(
      products: (json['products'] as List<dynamic>?)
              ?.map((p) => GroceryProduct.fromJson(p))
              .toList() ??
          [],
      count: json['count'] ?? 0,
      hasNext: json['has_next'] ?? false,
    );
  }
}

class SearchResponse {
  final String keyword;
  final Map<String, StoreProducts> stores;
  final int totalProducts;

  SearchResponse({
    required this.keyword,
    required this.stores,
    required this.totalProducts,
  });

  factory SearchResponse.fromJson(Map<String, dynamic> json) {
    final Map<String, StoreProducts> storesMap = {};

    if (json['stores'] != null) {
      final storesJson = json['stores'] as Map<String, dynamic>;
      for (final entry in storesJson.entries) {
        storesMap[entry.key] = StoreProducts.fromJson(entry.value);
      }
    }

    return SearchResponse(
      keyword: json['keyword'] ?? '',
      stores: storesMap,
      totalProducts: json['total_products'] ?? 0,
    );
  }

  List<GroceryProduct> get allProducts {
    final List<GroceryProduct> allProducts = [];
    for (final storeProducts in stores.values) {
      allProducts.addAll(storeProducts.products);
    }
    return allProducts;
  }

  bool get hasResults => totalProducts > 0;
}
