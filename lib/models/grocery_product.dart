class GroceryProduct {
  final String id;
  final String name;
  final String shopName;
  final String description;
  final double price;
  final double discount;
  final bool isOnPromotion;
  final List<String> images;
  final List<Promotion> promotions;
  final List<String>? displayCategories;

  GroceryProduct({
    required this.id,
    required this.name,
    required this.shopName,
    required this.description,
    required this.price,
    required this.discount,
    required this.isOnPromotion,
    required this.images,
    required this.promotions,
    this.displayCategories,
  });

  factory GroceryProduct.fromJson(Map<String, dynamic> json) {
    return GroceryProduct(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      shopName: json['shop_name'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      discount: (json['discount'] ?? 0.0).toDouble(),
      isOnPromotion: json['isOnPromotion'] ?? false,
      images: List<String>.from(json['images'] ?? []),
      promotions: (json['promotions'] as List<dynamic>?)
              ?.map((p) => Promotion.fromJson(p))
              .toList() ??
          [],
      displayCategories: json['displayCategories'] != null
          ? List<String>.from(json['displayCategories'])
          : null,
    );
  }

  // Factory method to create GroceryProduct from Budget Basket API response
  factory GroceryProduct.fromApiJson(Map<String, dynamic> json) {
    // Helper to safely parse numbers
    double safeParseDouble(dynamic value) {
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    return GroceryProduct(
      id: json['id']?.toString() ?? '',
      name: json['product_name'] ?? '',
      shopName: 'Shoprite', // Since this is from shoprite endpoint
      description: json['message_text'] ?? json['category'] ?? '',
      price: safeParseDouble(json['current_price']),
      discount:
          json['is_on_sale'] == true && json['discount_percentage'] != null
              ? (safeParseDouble(json['discount_percentage']) / 100.0)
              : 0.0,
      isOnPromotion: json['is_on_sale'] ?? false,
      images:
          json['image_url'] != null && json['image_url'].toString().isNotEmpty
              ? [json['image_url'].toString()]
              : [],
      promotions: json['is_on_sale'] == true
          ? [
              Promotion(
                name: 'Sale',
                description: 'Special offer',
                discountValue: safeParseDouble(json['discount_percentage']),
                startDate: '',
                endDate: '',
              )
            ]
          : [],
      displayCategories: json['category'] != null
          ? json['category'].toString().split('/')
          : [],
    );
  }

  String get formattedPrice {
    return 'R${price.toStringAsFixed(2)}';
  }

  String get discountedPrice {
    if (discount > 0) {
      final discountedAmount = price - (price * discount);
      return 'R${discountedAmount.toStringAsFixed(2)}';
    }
    return formattedPrice;
  }

  String get primaryImage {
    return images.isNotEmpty ? images.first : '';
  }

  bool get hasDiscount {
    return discount > 0 || isOnPromotion;
  }

  String get primaryCategory {
    return displayCategories?.isNotEmpty == true
        ? displayCategories!.first
        : '';
  }

  List<String> get safeDisplayCategories {
    return displayCategories ?? [];
  }
}

class Promotion {
  final String name;
  final String description;
  final double discountValue;
  final String startDate;
  final String endDate;

  Promotion({
    required this.name,
    required this.description,
    required this.discountValue,
    required this.startDate,
    required this.endDate,
  });

  factory Promotion.fromJson(Map<String, dynamic> json) {
    return Promotion(
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      discountValue: (json['discountValue'] ?? 0.0).toDouble(),
      startDate: json['startDate'] ?? '',
      endDate: json['endDate'] ?? '',
    );
  }
}

class ProductsResponse {
  final List<GroceryProduct> products;
  final int totalCount;
  final int page;
  final int pageSize;
  final bool hasNext;

  ProductsResponse({
    required this.products,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.hasNext,
  });

  factory ProductsResponse.fromJson(Map<String, dynamic> json) {
    return ProductsResponse(
      products: (json['products'] as List<dynamic>?)
              ?.map((p) => GroceryProduct.fromApiJson(p))
              .toList() ??
          [],
      totalCount: json['total_count'] ?? 0,
      page: json['page'] ?? 1,
      pageSize: json['page_size'] ?? 20,
      hasNext: json['has_next'] ?? false,
    );
  }

  // Helper getters for backward compatibility
  bool get success => products.isNotEmpty;

  ProductsData get data => ProductsData(
        products: products,
        totalCount: totalCount,
        page: page,
        pageSize: pageSize,
        hasNext: hasNext,
      );

  PaginationInfo get pagination => PaginationInfo(
        currentPage: page,
        pageSize: pageSize,
        totalCount: totalCount,
        hasNext: hasNext,
        hasPrevious: page > 1,
        totalPages: (totalCount / pageSize).ceil(),
      );
}

class ProductsData {
  final List<GroceryProduct> products;
  final int totalCount;
  final int page;
  final int pageSize;
  final bool hasNext;

  ProductsData({
    required this.products,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.hasNext,
  });

  factory ProductsData.fromJson(Map<String, dynamic> json) {
    return ProductsData(
      products: (json['products'] as List<dynamic>?)
              ?.map((p) => GroceryProduct.fromApiJson(p))
              .toList() ??
          [],
      totalCount: json['total_count'] ?? 0,
      page: json['page'] ?? 1,
      pageSize: json['page_size'] ?? 20,
      hasNext: json['has_next'] ?? false,
    );
  }
}

class PaginationInfo {
  final int currentPage;
  final int pageSize;
  final int totalCount;
  final bool hasNext;
  final bool hasPrevious;
  final int totalPages;

  PaginationInfo({
    required this.currentPage,
    required this.pageSize,
    required this.totalCount,
    required this.hasNext,
    required this.hasPrevious,
    required this.totalPages,
  });

  factory PaginationInfo.fromJson(Map<String, dynamic> json) {
    return PaginationInfo(
      currentPage: json['current_page'] ?? 1,
      pageSize: json['page_size'] ?? 20,
      totalCount: json['total_count'] ?? 0,
      hasNext: json['has_next'] ?? false,
      hasPrevious: json['has_previous'] ?? false,
      totalPages: json['total_pages'] ?? 1,
    );
  }
}
