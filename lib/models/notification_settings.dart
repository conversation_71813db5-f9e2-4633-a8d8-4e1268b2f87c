class NotificationSettings {
  final String userId;
  final String userEmail;
  final DateTime lastUpdated;
  final NotificationPreferences preferences;
  final NotificationCategories categories;
  final String frequency;
  final List<String> stores;

  NotificationSettings({
    required this.userId,
    required this.userEmail,
    required this.lastUpdated,
    required this.preferences,
    required this.categories,
    required this.frequency,
    required this.stores,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      userId: json['userId'] ?? '',
      userEmail: json['userEmail'] ?? '',
      lastUpdated: DateTime.parse(json['lastUpdated']),
      preferences: NotificationPreferences.fromJson(json['preferences']),
      categories: NotificationCategories.fromJson(json['categories']),
      frequency: json['frequency'] ?? 'daily',
      stores: List<String>.from(json['stores'] ?? []),
    );
  }

  Map<String, dynamic> toJson() => {
    'userId': userId,
    'userEmail': userEmail,
    'lastUpdated': lastUpdated.toIso8601String(),
    'preferences': preferences.toJson(),
    'categories': categories.toJson(),
    'frequency': frequency,
    'stores': stores,
  };
}

class NotificationPreferences {
  final bool pushNotifications;
  final bool emailNotifications;
  final bool specialOffers;

  NotificationPreferences({
    required this.pushNotifications,
    required this.emailNotifications,
    required this.specialOffers,
  });

  factory NotificationPreferences.fromJson(Map<String, dynamic> json) {
    return NotificationPreferences(
      pushNotifications: json['pushNotifications'] ?? false,
      emailNotifications: json['emailNotifications'] ?? false,
      specialOffers: json['specialOffers'] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
    'pushNotifications': pushNotifications,
    'emailNotifications': emailNotifications,
    'specialOffers': specialOffers,
  };

  NotificationPreferences copyWith({
    bool? pushNotifications,
    bool? emailNotifications,
    bool? specialOffers,
  }) {
    return NotificationPreferences(
      pushNotifications: pushNotifications ?? this.pushNotifications,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      specialOffers: specialOffers ?? this.specialOffers,
    );
  }
}

class NotificationCategories {
  final bool priceAlerts;
  final bool newProducts;
  final bool stockAlerts;

  NotificationCategories({
    required this.priceAlerts,
    required this.newProducts,
    required this.stockAlerts,
  });

  factory NotificationCategories.fromJson(Map<String, dynamic> json) {
    return NotificationCategories(
      priceAlerts: json['priceAlerts'] ?? false,
      newProducts: json['newProducts'] ?? false,
      stockAlerts: json['stockAlerts'] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
    'priceAlerts': priceAlerts,
    'newProducts': newProducts,
    'stockAlerts': stockAlerts,
  };

  NotificationCategories copyWith({
    bool? priceAlerts,
    bool? newProducts,
    bool? stockAlerts,
  }) {
    return NotificationCategories(
      priceAlerts: priceAlerts ?? this.priceAlerts,
      newProducts: newProducts ?? this.newProducts,
      stockAlerts: stockAlerts ?? this.stockAlerts,
    );
  }
}
