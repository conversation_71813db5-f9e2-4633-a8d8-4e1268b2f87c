class OrderItem {
  final String id;
  final String barcode;
  final String name;
  final String brand;
  final String category;
  final String price;
  final double numericPrice;
  final int quantity;
  final double totalPrice;
  final String imageUrl;

  OrderItem({
    required this.id,
    required this.barcode,
    required this.name,
    required this.brand,
    required this.category,
    required this.price,
    required this.numericPrice,
    required this.quantity,
    required this.totalPrice,
    required this.imageUrl,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      id: json['id'] ?? '',
      barcode: json['barcode'] ?? '',
      name: json['name'] ?? '',
      brand: json['brand'] ?? '',
      category: json['category'] ?? '',
      price: json['price'] ?? '',
      numericPrice: (json['numericPrice'] ?? 0).toDouble(),
      quantity: json['quantity'] ?? 0,
      totalPrice: (json['totalPrice'] ?? 0).toDouble(),
      imageUrl: json['image_url'] ?? '',
    );
  }

  String get sanitizedImageUrl {
    if (imageUrl.isEmpty || 
        imageUrl == 'null' || 
        imageUrl == 'undefined' ||
        imageUrl.contains('placeholder.com')) {
      return '';
    }
    
    // Handle relative URLs by adding the base domain
    if (!imageUrl.startsWith('http')) {
      return 'https://www.kemavuso.co.za/budget$imageUrl';
    }
    
    return imageUrl;
  }
}
