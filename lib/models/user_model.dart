import 'dart:convert';

class NotificationPreferences {
  final String userId;
  final String userEmail;
  final String lastUpdated;
  final Map<String, dynamic> preferences;
  final Map<String, dynamic> categories;
  final dynamic frequency;
  final List<dynamic> stores;

  NotificationPreferences({
    required this.userId,
    required this.userEmail,
    required this.lastUpdated,
    required this.preferences,
    required this.categories,
    required this.frequency,
    required this.stores,
  });

  factory NotificationPreferences.fromJson(Map<String, dynamic> json) {
    Map<String, dynamic> preferencesMap = {};
    if (json['preferences'] is Map) {
      preferencesMap = Map<String, dynamic>.from(json['preferences'] ?? {});
    }

    Map<String, dynamic> categoriesMap = {};
    if (json['categories'] is Map) {
      categoriesMap = Map<String, dynamic>.from(json['categories'] ?? {});
    }

    List<dynamic> storesList = [];
    if (json['stores'] is List) {
      storesList = List.from(json['stores'] ?? []);
    } else if (json['stores'] is Map) {
      storesList = [Map<String, dynamic>.from(json['stores'])];
    }

    return NotificationPreferences(
      userId: json['userId'] ?? '',
      userEmail: json['userEmail'] ?? '',
      lastUpdated: json['lastUpdated'] ?? '',
      preferences: preferencesMap,
      categories: categoriesMap,
      frequency: json['frequency'] ?? '',
      stores: storesList,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userEmail': userEmail,
      'lastUpdated': lastUpdated,
      'preferences': preferences,
      'categories': categories,
      'frequency': frequency,
      'stores': stores,
    };
  }

  factory NotificationPreferences.createDefault(String userId, String email) {
    final now = DateTime.now().toUtc().toIso8601String();
    return NotificationPreferences(
      userId: userId,
      userEmail: email,
      lastUpdated: now,
      preferences: {
        'pushNotifications': false,
        'emailNotifications': false,
        'specialOffers': false,
      },
      categories: {
        'priceAlerts': false,
        'newProducts': false,
        'stockAlerts': false,
      },
      frequency: 'daily',
      stores: [],
    );
  }
}

class User {
  final String id;
  final String fullname;
  final String emailAddress;
  final String status;
  final NotificationPreferences notificationSettings;

  User({
    required this.id,
    required this.fullname,
    required this.emailAddress,
    required this.status,
    required this.notificationSettings,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    print('Inside User.fromJson');
    try {
      NotificationPreferences notificationSettings;
      final rawSettings = json['notification_settings'];

      print('Raw notification_settings type: ${rawSettings.runtimeType}');

      if (rawSettings is Map) {
        print('notification_settings is a Map');
        notificationSettings = NotificationPreferences.fromJson(
          Map<String, dynamic>.from(rawSettings)
        );
      } else if (rawSettings is String) {
        print('notification_settings is a String, trying to parse');
        try {
          final parsedSettings = jsonDecode(rawSettings);
          notificationSettings = NotificationPreferences.fromJson(
            Map<String, dynamic>.from(parsedSettings)
          );
        } catch (e) {
          print('Error parsing notification_settings string: $e');
          notificationSettings = NotificationPreferences.createDefault(
            json['id'] ?? '', 
            json['email_address'] ?? ''
          );
        }
      } else {
        print('Using default notification_settings');
        notificationSettings = NotificationPreferences.createDefault(
          json['id'] ?? '', 
          json['email_address'] ?? ''
        );
      }

      return User(
        id: json['id'] ?? '',
        fullname: json['fullname'] ?? '',
        emailAddress: json['email_address'] ?? '',
        status: json['status'] ?? '',
        notificationSettings: notificationSettings,
      );
    } catch (e) {
      print('Error in User.fromJson: $e');
      print('Stack trace: ${StackTrace.current}');

      return User(
        id: json['id'] ?? '',
        fullname: json['fullname'] ?? '',
        emailAddress: json['email_address'] ?? '',
        status: json['status'] ?? '',
        notificationSettings: NotificationPreferences.createDefault(
          json['id'] ?? '', 
          json['email_address'] ?? ''
        ),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullname': fullname,
      'email_address': emailAddress,
      'status': status,
      'notification_settings': notificationSettings.toJson(),
    };
  }

  factory User.createNew(String fullname, String email) {
    return User(
      id: '', 
      fullname: fullname,
      emailAddress: email,
      status: 'active',
      notificationSettings: NotificationPreferences.createDefault('', email),
    );
  }

  String toJsonString() {
    return jsonEncode(toJson());
  }
}
