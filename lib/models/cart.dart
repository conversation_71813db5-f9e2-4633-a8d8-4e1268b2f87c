import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'product.dart';

class Cart with ChangeNotifier {
  final List<Product> _items = [];
  double _budget = 0.0;
  String _selectedStore = ''; // No default store

  // Constants for limits
  static const int maxItemCount = 200;
  static const double maxTotalAmount = 200000.0;

  List<Product> get items => [..._items];
  double get budget => _budget;
  String get selectedStore => _selectedStore;

  // Total price of all items in cart
  double get totalPrice {
    return _items.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  // Total number of items in cart
  int get itemCount {
    return _items.length;
  }

  // Total quantity of all items in cart
  int get totalQuantity {
    return _items.fold(0, (sum, item) => sum + item.quantity);
  }

  // Check if we're over budget
  bool get isOverBudget {
    return totalPrice > _budget && _budget > 0;
  }

  // Check if cart has reached maximum item count
  bool get hasReachedMaxItems {
    return totalQuantity >= maxItemCount;
  }

  // Check if cart has reached maximum total amount
  bool get hasReachedMaxAmount {
    return totalPrice >= maxTotalAmount;
  }

  // Remaining budget
  double get remainingBudget {
    return _budget - totalPrice;
  }

  // Set budget
  void setBudget(double budget) {
    _budget = budget;
    notifyListeners();
  }

  // Set selected store
  void setStore(String store) {
    _selectedStore = store;
    notifyListeners();
  }

  // Clear selected store
  void clearStore() {
    _selectedStore = '';
    notifyListeners();
  }

  // Add product to cart
  bool addProduct(Product product) {
    // Check if adding this product would exceed the maximum item count
    final int currentTotalQuantity = totalQuantity;
    final bool wouldExceedMaxItems =
        currentTotalQuantity + product.quantity > maxItemCount;

    // Check if adding this product would exceed the maximum total amount
    final double newTotalPrice = totalPrice + product.totalPrice;
    final bool wouldExceedMaxAmount = newTotalPrice > maxTotalAmount;

    // If either limit would be exceeded, don't add the product
    if (wouldExceedMaxItems || wouldExceedMaxAmount) {
      return false;
    }

    final existingIndex = _items.indexWhere((item) => item.id == product.id);

    if (existingIndex >= 0) {
      // Product already exists, increment quantity
      _items[existingIndex] = _items[existingIndex].copyWith(
          quantity: _items[existingIndex].quantity + product.quantity);
    } else {
      // Add new product
      _items.add(product);
    }
    notifyListeners();
    return true;
  }

  // Update product quantity
  bool updateQuantity(String productId, int quantity) {
    final index = _items.indexWhere((item) => item.id == productId);
    if (index < 0) return false;

    if (quantity <= 0) {
      _items.removeAt(index);
      notifyListeners();
      return true;
    }

    // Calculate the change in quantity
    final int quantityDifference = quantity - _items[index].quantity;

    // Check if this change would exceed the maximum item count
    final int newTotalQuantity = totalQuantity + quantityDifference;
    if (newTotalQuantity > maxItemCount) {
      return false;
    }

    // Check if this change would exceed the maximum total amount
    final double pricePerUnit = _items[index].numericPrice;
    final double priceDifference = pricePerUnit * quantityDifference;
    final double newTotalPrice = totalPrice + priceDifference;
    if (newTotalPrice > maxTotalAmount) {
      return false;
    }

    // Apply the update
    _items[index] = _items[index].copyWith(quantity: quantity);
    notifyListeners();
    return true;
  }

  // Remove product from cart
  void removeProduct(String productId) {
    _items.removeWhere((item) => item.id == productId);
    notifyListeners();
  }

  // Clear cart
  void clear() {
    _items.clear();
    notifyListeners();
  }
}
