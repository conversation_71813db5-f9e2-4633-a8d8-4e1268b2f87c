class Product {
  final String searchBarcode;
  final String id;
  final String store;
  final String name;
  final String price;
  final String brand;
  final String category;
  final String imageUrl;
  final String url;
  final String originalImageUrl;
  final int quantity;

  // Calculate numeric price from string price
  double get numericPrice {
    try {
      // If price is empty, return 0
      if (price.isEmpty) {
        print('Empty price string, returning 0.0');
        return 0.0;
      }
      
      // Remove 'R' prefix, spaces, and any other non-numeric characters except decimal point
      String cleanPrice = price.replaceAll('R', '')
                               .replaceAll(' ', '')
                               .trim();
      
      // Handle comma as decimal separator
      if (cleanPrice.contains(',') && !cleanPrice.contains('.')) {
        cleanPrice = cleanPrice.replaceAll(',', '.');
      }
      
      // Remove any remaining non-numeric characters except decimal point
      cleanPrice = cleanPrice.replaceAll(RegExp(r'[^\d.]'), '');
      
      print('Parsing price: "$price" -> cleaned to: "$cleanPrice"');
      
      if (cleanPrice.isEmpty) {
        print('Cleaned price is empty, returning 0.0');
        return 0.0;
      }
      
      final parsedPrice = double.parse(cleanPrice);
      print('Successfully parsed price: $parsedPrice');
      return parsedPrice;
    } catch (e) {
      print('Error parsing price: $e for price string: "$price"');
      return 0.0;
    }
  }

  // Format price for display
  String get formattedPrice {
    if (price.isEmpty) {
      print('Empty price, returning R0.00');
      return 'R0.00';
    }
    
    try {
      final numPrice = numericPrice;
      
      // If numeric price is 0 but original price isn't empty, 
      // try to display the original price
      if (numPrice == 0.0 && price.isNotEmpty && price != '0' && price != 'R0') {
        if (price.startsWith('R')) {
          return price;
        } else {
          return 'R$price';
        }
      }
      
      return 'R${numPrice.toStringAsFixed(2)}';
    } catch (e) {
      print('Error formatting price: $e');
      return 'R0.00';
    }
  }

  // Get a sanitized image URL
  String get sanitizedImageUrl {
    if (imageUrl.isEmpty || 
        imageUrl == 'null' || 
        imageUrl == 'undefined' ||
        imageUrl.contains('placeholder.com')) {
      return '';
    }
    return imageUrl;
  }

  // Get the total price (price * quantity)
  double get totalPrice {
    return numericPrice * quantity;
  }

  Product({
    required this.searchBarcode,
    required this.id,
    required this.name,
    required this.store,
    required this.price,
    required this.brand,
    required this.category,
    required this.imageUrl,
    required this.url,
    required this.originalImageUrl,
    this.quantity = 1,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      searchBarcode: json['search_barcode'] ?? '',
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      store: json['store'] ?? '',
      price: json['price'] ?? '',
      brand: json['brand'] ?? '',
      category: json['category'] ?? '',
      imageUrl: json['image_url'] ?? '',
      url: json['url'] ?? '',
      originalImageUrl: json['original_image_url'] ?? '',
      quantity: json['quantity'] ?? 1,
    );
  }

  // Create a copy of this product with updated fields
  Product copyWith({
    String? searchBarcode,
    String? id,
    String? name,
    String? price,
    String? brand,
    String? category,
    String? imageUrl,
    String? url,
    int? quantity,
  }) {
    return Product(
      searchBarcode: searchBarcode ?? this.searchBarcode,
      id: id ?? this.id,
      store: store ?? this.store,
      name: name ?? this.name,
      price: price ?? this.price,
      brand: brand ?? this.brand,
      category: category ?? this.category,
      imageUrl: imageUrl ?? this.imageUrl,
      url: url ?? this.url,
      originalImageUrl: originalImageUrl,
      quantity: quantity ?? this.quantity,
    );
  }
}
