class User {
  final String id;
  final String displayName;
  final String email;
  final String photoUrl;

  User({
    required this.id,
    required this.displayName,
    required this.email,
    required this.photoUrl,
  });

  // Create a User from Firebase User
  factory User.fromFirebase(dynamic firebaseUser) {
    return User(
      id: firebaseUser.uid ?? '',
      displayName: firebaseUser.displayName ?? 'Guest',
      email: firebaseUser.email ?? '',
      photoUrl: firebaseUser.photoURL ?? '',
    );
  }

  // Create an empty user
  factory User.empty() {
    return User(
      id: '',
      displayName: 'Guest',
      email: '',
      photoUrl: '',
    );
  }

  // Copy with method for updating user data
  User copyWith({
    String? id,
    String? displayName,
    String? email,
    String? photoUrl,
  }) {
    return User(
      id: id ?? this.id,
      displayName: displayName ?? this.displayName,
      email: email ?? this.email,
      photoUrl: photoUrl ?? this.photoUrl,
    );
  }
}
