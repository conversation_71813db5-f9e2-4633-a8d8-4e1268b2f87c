class Category {
  final String category;
  final int productCount;

  Category({
    required this.category,
    required this.productCount,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      category: json['category'] ?? '',
      productCount: json['product_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'product_count': productCount,
    };
  }

  String get name => category;

  String get topLevelCategory {
    if (category.isEmpty) return '';
    return category.split('/').first;
  }

  String get displayName {
    if (category.isEmpty) return '';
    // Return the last part of the path, with dashes replaced by spaces.
    return category.split('/').last.replaceAll('-', ' ');
  }
}

class CategoriesResponse {
  final List<Category> categories;
  final int totalCategories;

  CategoriesResponse({
    required this.categories,
    required this.totalCategories,
  });

  factory CategoriesResponse.fromJson(Map<String, dynamic> json) {
    final List<dynamic> categoriesJson = json['categories'] ?? [];
    final categories =
        categoriesJson.map((category) => Category.fromJson(category)).toList();

    return CategoriesResponse(
      categories: categories,
      totalCategories: json['total_categories'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'categories': categories.map((c) => c.toJson()).toList(),
      'total_categories': totalCategories,
    };
  }
}
