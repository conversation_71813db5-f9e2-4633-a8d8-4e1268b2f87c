class Special {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final String store;
  final String validUntil;

  Special({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.store,
    required this.validUntil,
  });

  factory Special.fromJson(Map<String, dynamic> json) {
    return Special(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      store: json['store'] ?? '',
      validUntil: json['validUntil'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'imageUrl': imageUrl,
      'store': store,
      'validUntil': validUntil,
    };
  }
}
