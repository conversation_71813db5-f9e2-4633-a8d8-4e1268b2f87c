import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// Example demonstrating how to load Shoprite images with proper headers
/// to bypass 403 Forbidden errors
class ShopriteImageExample extends StatelessWidget {
  const ShopriteImageExample({super.key});

  @override
  Widget build(BuildContext context) {
    // Example Shoprite image URL
    const String shopriteImageUrl = 
        'https://www.shoprite.co.za/medias/checkers300Wx300H-10793841EA.png?context=bWFzdGVyfGltYWdlc3w2NDM4MHxpbWFnZS9wbmd8aW1hZ2VzL2g4Mi9oN2MvMTE1NjY5NDg0MTc1NjYucG5nfDYwYzNjYzg5N2Y5NzVjNmRkMWQ0NThkMzBhODdhMmU2YTRlZDY2ZGQ4Y2FjNzQ1YWZkNTUyMDhjYTEwNGJjM2M';

    return Scaffold(
      appBar: AppBar(
        title: const Text('Shoprite Image Loading Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'CachedNetworkImage with Custom Headers',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Example 1: Using CachedNetworkImage directly
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CachedNetworkImage(
                imageUrl: shopriteImageUrl,
                httpHeaders: {
                  'User-Agent':
                      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                  'Accept':
                      'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                  'Accept-Language': 'en-US,en;q=0.9',
                  'Accept-Encoding': 'gzip, deflate, br',
                  'Referer': 'https://www.shoprite.co.za/',
                  'Sec-Fetch-Dest': 'image',
                  'Sec-Fetch-Mode': 'no-cors',
                  'Sec-Fetch-Site': 'same-origin',
                  'Sec-Ch-Ua':
                      '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"',
                  'Sec-Ch-Ua-Mobile': '?0',
                  'Sec-Ch-Ua-Platform': '"Windows"',
                },
                fit: BoxFit.cover,
                placeholder: (context, url) => const Center(
                  child: CircularProgressIndicator(),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[200],
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.broken_image,
                        size: 50,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Failed to load image',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            const Text(
              'Key Points:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            
            const Text('• CachedNetworkImage supports custom HTTP headers'),
            const Text('• Headers mimic a real browser request'),
            const Text('• Referer header is crucial for Shoprite images'),
            const Text('• Images are cached for better performance'),
            const Text('• Graceful fallback for failed image loads'),
            
            const SizedBox(height: 24),
            
            const Text(
              'Benefits over Image.network():',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            
            const Text('✅ Better header support'),
            const Text('✅ Automatic caching'),
            const Text('✅ Better error handling'),
            const Text('✅ Loading placeholders'),
            const Text('✅ Memory management'),
          ],
        ),
      ),
    );
  }
}
