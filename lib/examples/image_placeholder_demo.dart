import 'package:flutter/material.dart';
import '../widgets/network_image_with_fallback.dart';

/// Demo showing the improved image loading with consistent styling
class ImagePlaceholderDemo extends StatelessWidget {
  const ImagePlaceholderDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Image Loading Demo'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Image Loading Examples',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            
            // Example 1: Working image
            Text(
              '✅ Working Image:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: NetworkImageWithFallback(
                  imageUrl: 'https://picsum.photos/300/150',
                  fit: BoxFit.cover,
                  fallbackText: 'Sample Product Name',
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Example 2: Failed image (placeholder)
            Text(
              '❌ Failed Image (Shows Placeholder):',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: NetworkImageWithFallback(
                  imageUrl: 'https://invalid-url-that-will-fail.com/image.jpg',
                  fit: BoxFit.cover,
                  fallbackText: 'Organic Fresh Vegetables Premium Quality',
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Example 3: Empty URL
            Text(
              '🚫 Empty URL (Shows Placeholder):',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: NetworkImageWithFallback(
                  imageUrl: '',
                  fit: BoxFit.cover,
                  fallbackText: 'Product Without Image',
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            Text(
              'Key Improvements:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            Text('• Placeholders fill the full width'),
            Text('• Uses consistent theme fonts (bodySmall)'),
            Text('• Colored backgrounds for visual appeal'),
            Text('• Proper text truncation and overflow handling'),
            Text('• Cached network images for better performance'),
            Text('• Browser-like headers for protected CDNs'),
          ],
        ),
      ),
    );
  }
}
