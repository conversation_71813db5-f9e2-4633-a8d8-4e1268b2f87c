import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdMobService {
  static String get bannerAdUnitId {
    if (kDebugMode) {
      // Use test ad unit IDs in debug mode
      if (Platform.isAndroid) {
        return 'ca-app-pub-3940256099942544/6300978111'; // Android test banner ID
      } else if (Platform.isIOS) {
        return 'ca-app-pub-3940256099942544/2934735716'; // iOS test banner ID
      }
    }
    // Production ad unit IDs
    if (Platform.isAndroid) {
      return 'ca-app-pub-1066070623841373/4011800142'; // Your Android production ID
    } else if (Platform.isIOS) {
      return 'YOUR_IOS_BANNER_AD_UNIT_ID'; // Replace with your iOS production ID
    }
    throw UnsupportedError('Unsupported platform');
  }

  static final BannerAdListener bannerListener = BannerAdListener(
    onAdLoaded: (ad) => debugPrint('Banner ad loaded: ${ad.adUnitId}'),
    onAdFailedToLoad: (ad, error) {
      ad.dispose();
      debugPrint('Banner ad failed to load: ${ad.adUnitId}, ${error.message}');
    },
    onAdOpened: (ad) => debugPrint('Banner ad opened: ${ad.adUnitId}'),
    onAdClosed: (ad) => debugPrint('Banner ad closed: ${ad.adUnitId}'),
    onAdImpression: (ad) => debugPrint('Banner ad impression: ${ad.adUnitId}'),
    onAdClicked: (ad) => debugPrint('Banner ad clicked: ${ad.adUnitId}'),
  );

  static Future<AnchoredAdaptiveBannerAdSize?> getAdaptiveBannerAdSize(BuildContext context) async {
    // Get the width of the screen
    final width = MediaQuery.of(context).size.width.truncate();
    
    // Get the adaptive banner ad size based on the width
    return await AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(width);
  }

  static Future<BannerAd?> createAdaptiveBannerAd(BuildContext context) async {
    final AnchoredAdaptiveBannerAdSize? size = await getAdaptiveBannerAdSize(context);
    
    if (size == null) {
      debugPrint('Unable to get adaptive banner ad size');
      return null;
    }

    return BannerAd(
      adUnitId: bannerAdUnitId,
      size: size,
      request: const AdRequest(),
      listener: bannerListener,
    )..load();
  }

  static Future<void> initGoogleMobileAds() {
    if (kDebugMode) {
      MobileAds.instance.updateRequestConfiguration(
        RequestConfiguration(
          testDeviceIds: ['SIMULATOR'], // Add your test device IDs here
        ),
      );
    }
    return MobileAds.instance.initialize();
  }
}
