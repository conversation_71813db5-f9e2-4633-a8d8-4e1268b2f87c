import '../models/special.dart';

class SpecialService {
  // For demo purposes, we'll use mock data
  // In a real app, this would be replaced with actual API calls
  Future<List<Special>> getSpecials(String store) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Return mock data based on store
    return _getMockSpecials(store);
  }

  List<Special> _getMockSpecials(String store) {
    // Common specials for all stores
    final List<Special> commonSpecials = [
      Special(
        id: '1',
        title: 'Weekend Sale - 20% Off Fresh Produce',
        description:
            'Get 20% off all fresh fruits and vegetables this weekend only!',
        imageUrl:
            'https://images.unsplash.com/photo-1542838132-92c53300491e?q=80&w=1000',
        store: 'All Stores',
        validUntil: '2025-03-23',
      ),
      Special(
        id: '2',
        title: 'Buy 1 Get 1 Free - Breakfast Cereals',
        description:
            'Buy any breakfast cereal and get another one free. Limited time offer!',
        imageUrl:
            'https://images.unsplash.com/photo-1521483451569-e33803c0330c?q=80&w=1000',
        store: 'All Stores',
        validUntil: '2025-03-31',
      ),
    ];

    // Store-specific specials
    final Map<String, List<Special>> storeSpecials = {
      'Budget Basket': [
        Special(
          id: '3',
          title: 'Budget Basket Special - 30% Off Meat',
          description:
              'Budget Basket members get 30% off all meat products this week.',
          imageUrl:
              'https://images.unsplash.com/photo-1607623814075-e51df1bdc82f?q=80&w=1000',
          store: 'Budget Basket',
          validUntil: '2025-03-25',
        ),
        Special(
          id: '4',
          title: 'Budget Basket Wine Sale',
          description:
              'Up to 40% off selected wines when you buy 6 or more bottles.',
          imageUrl:
              'https://images.unsplash.com/photo-1506377247377-2a5b3b417ebb?q=80&w=1000',
          store: 'Budget Basket',
          validUntil: '2025-03-28',
        ),
      ],
      'Woolworths': [
        Special(
          id: '7',
          title: 'WRewards Member Special',
          description: 'WRewards members get 20% off all ready-made meals.',
          imageUrl:
              'https://images.unsplash.com/photo-1536304993881-ff6e9eefa2a6?q=80&w=1000',
          store: 'Woolworths',
          validUntil: '2025-03-26',
        ),
        Special(
          id: '8',
          title: 'Woolworths Beauty Sale',
          description: 'Up to 25% off selected beauty and skincare products.',
          imageUrl:
              'https://images.unsplash.com/photo-1596462502278-27bfdc403348?q=80&w=1000',
          store: 'Woolworths',
          validUntil: '2025-03-29',
        ),
      ],
      'Makro': [
        Special(
          id: '11',
          title: 'Makro Tech Sale',
          description:
              'Up to 40% off laptops, smartphones, and other electronics.',
          imageUrl:
              'https://images.unsplash.com/photo-1498049794561-7780e7231661?q=80&w=1000',
          store: 'Makro',
          validUntil: '2025-03-30',
        ),
        Special(
          id: '12',
          title: 'Makro Home Essentials',
          description:
              'Buy any 3 home essentials and get the cheapest one free.',
          imageUrl:
              'https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?q=80&w=1000',
          store: 'Makro',
          validUntil: '2025-03-28',
        ),
      ],
      'Default': [
        Special(
          id: '13',
          title: 'Budget Basket Exclusive Deal',
          description:
              'Special offers curated for Budget Basket users across all stores.',
          imageUrl:
              'https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?q=80&w=1000',
          store: 'Default',
          validUntil: '2025-04-15',
        ),
        Special(
          id: '14',
          title: 'Seasonal Promotions',
          description:
              'Check out the best seasonal deals from various retailers.',
          imageUrl:
              'https://images.unsplash.com/photo-1583258292688-d0213dc5a3a8?q=80&w=1000',
          store: 'Default',
          validUntil: '2025-04-10',
        ),
      ],
    };

    // Return common specials plus store-specific specials if available
    if (store.isEmpty) {
      // If no store selected, return default specials
      return [...commonSpecials, ...storeSpecials['Default'] ?? []];
    } else if (store == 'Default') {
      // If Default is selected, return default specials
      return [...commonSpecials, ...storeSpecials['Default'] ?? []];
    } else if (storeSpecials.containsKey(store)) {
      // Return store-specific specials
      return [...commonSpecials, ...storeSpecials[store] ?? []];
    } else {
      // If store not found, return just common specials
      return commonSpecials;
    }
  }
}
