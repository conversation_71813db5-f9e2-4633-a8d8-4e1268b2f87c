import 'package:flutter/material.dart';
import '../config/api_keys.dart';
import 'deep_link_service.dart';

enum TransactionStatus {
  processing,
  success,
  failed,
  cancelled,
}

class PaymentService extends ChangeNotifier {
  // Ozow payment configuration
  static const String siteCode = ApiKeys.ozowSiteCode;
  static const String privateKey = ApiKeys.ozowPrivateKey;
  static const String apiKey = ApiKeys.ozowApiKey;
  static const bool isTest = true;

  String generateTransactionId() {
    String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    String base = "BB$timestamp";
    return base.length > 20 ? base.substring(0, 20) : base.padRight(20, '0');
  }

  // Check if privacy disclaimer has been shown
  Future<bool> hasShownPrivacyDisclaimer() async {
    return true; // Privacy disclaimer is now shown by default
  }

  // Show privacy disclaimer dialog
  Future<bool> showPrivacyDisclaimerIfNeeded(BuildContext context) async {
    return true; // Privacy disclaimer is now shown by default
  }

  // Process payment using Ozow
  Widget processPayment({
    required String amount,
    required String bankRef,
    required Function(TransactionStatus) onStatusChange,
  }) {
    final transactionId = generateTransactionId();
    final url = _buildPaymentUrl(amount, bankRef, transactionId);
    
    // Launch payment URL
    DeepLinkService.handlePaymentUrl(url);
    
    return const SizedBox.shrink();
  }

  String _buildPaymentUrl(String amount, String bankRef, String transactionId) {
    return 'https://pay.ozow.com?' +
      'SiteCode=$siteCode' +
      '&TransactionId=$transactionId' +
      '&Amount=$amount' +
      '&countryCode=ZA' +
      '&currencyCode=ZAR' +
      '&transactionReference=$transactionId' +
      '&BankReference=$bankRef' +
      '&SuccessUrl=budgetbasket://app/success' +
      '&ErrorUrl=budgetbasket://app/error' +
      '&CancelUrl=budgetbasket://app/cancel' +
      '&NotifyUrl=budgetbasket://app/notify' +
      '&IsTest=$isTest';
  }
}
