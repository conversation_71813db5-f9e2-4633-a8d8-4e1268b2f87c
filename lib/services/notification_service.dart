import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../models/notification_settings.dart';
import '../services/secure_storage_service.dart';
import '../services/auth_service.dart';

class NotificationService with ChangeNotifier {
  static const String baseUrl = 'https://www.kemavuso.co.za/budget';
  static const String notificationSettingsKey = 'notification_settings';
  
  NotificationSettings? _settings;
  bool _isLoading = false;
  String? _error;

  NotificationSettings? get settings => _settings;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load settings from secure storage
  Future<NotificationSettings?> _loadFromSecureStorage() async {
    try {
      final settingsJson = await SecureStorageService.getNotificationSettings();
      
      if (settingsJson != null && settingsJson.isNotEmpty) {
        return NotificationSettings.fromJson(jsonDecode(settingsJson));
      }
    } catch (e) {
      debugPrint('Error loading notification settings from secure storage: $e');
    }
    return null;
  }

  // Save settings to secure storage
  Future<void> _saveToSecureStorage(NotificationSettings settings) async {
    try {
      await SecureStorageService.saveNotificationSettings(
        jsonEncode(settings.toJson()),
      );
    } catch (e) {
      debugPrint('Error saving notification settings to secure storage: $e');
    }
  }

  // Initialize settings - load from secure storage first, then fetch from API if needed
  Future<void> initializeSettings() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Try to load from secure storage first
      final savedSettings = await _loadFromSecureStorage();
      if (savedSettings != null) {
        _settings = savedSettings;
        _isLoading = false;
        notifyListeners();
        return;
      }

      // If no saved settings, fetch from API
      await fetchNotificationsFromApi();
    } catch (e) {
      _error = 'Error initializing notification settings: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fetch settings from API
  Future<void> fetchNotificationsFromApi() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      debugPrint('Fetching notification settings from API');
      
      // Get user ID from secure storage for debugging
      final userId = await SecureStorageService.getUserId();
      debugPrint('User ID from secure storage: $userId');
      
      // Force refresh the token before making the API call
      final authService = AuthService();
      final tokenRefreshed = await authService.forceRefreshAndSaveToken();
      debugPrint('Token refresh result: $tokenRefreshed');
      
      // Get the token for debugging
      final token = await SecureStorageService.getFirebaseJwtToken();
      debugPrint('JWT Token before API call: ${token != null ? (token.length > 20 ? '${token.substring(0, 20)}...' : token) : 'null'}');
      
      // Construct the URL with user ID if available
      String endpoint = '$baseUrl/notifications';
      if (userId != null && userId.isNotEmpty) {
        endpoint = '$baseUrl/notifications/$userId';
        debugPrint('Using endpoint with user ID: $endpoint');
      } else {
        debugPrint('No user ID available, using default endpoint: $endpoint');
      }
      
      final response = await http.get(
        Uri.parse(endpoint),
        headers: await _getHeaders(),
      );

      debugPrint('Notification settings API response status: ${response.statusCode}');
      debugPrint('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _settings = NotificationSettings.fromJson(data);
        
        // Save to secure storage
        await _saveToSecureStorage(_settings!);
        
        _error = null;
      } else {
        _error = 'Failed to fetch notification settings: ${response.statusCode} - Response: ${response.body}';
        debugPrint(_error);
      }
    } catch (e) {
      _error = 'Error fetching notification settings: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get headers with authorization token
  Future<Map<String, String>> _getHeaders() async {
    final token = await SecureStorageService.getFirebaseJwtToken();
    if (token == null) {
      debugPrint('No JWT token found in secure storage');
      return {'Content-Type': 'application/json'};
    }
    
    debugPrint('Adding Authorization header with token');
    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }

  // Update settings
  Future<bool> updateSettings({
    bool? pushNotifications,
    bool? emailNotifications,
    bool? specialOffers,
    bool? priceAlerts,
    bool? newProducts,
    bool? stockAlerts,
  }) async {
    if (_settings == null) return false;

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Get user ID from secure storage for debugging
      final userId = await SecureStorageService.getUserId();
      debugPrint('User ID from secure storage for update: $userId');
      
      // Force refresh the token before making the API call
      final authService = AuthService();
      final tokenRefreshed = await authService.forceRefreshAndSaveToken();
      debugPrint('Token refresh result: $tokenRefreshed');
      
      // Get the token for debugging
      final token = await SecureStorageService.getFirebaseJwtToken();
      debugPrint('JWT Token before API call: ${token != null ? (token.length > 20 ? '${token.substring(0, 20)}...' : token) : 'null'}');
      
      final updatedSettings = NotificationSettings(
        userId: userId ?? _settings!.userId,
        userEmail: _settings!.userEmail,
        lastUpdated: DateTime.now().toUtc(),
        preferences: NotificationPreferences(
          pushNotifications: pushNotifications ?? _settings!.preferences.pushNotifications,
          emailNotifications: emailNotifications ?? _settings!.preferences.emailNotifications,
          specialOffers: specialOffers ?? _settings!.preferences.specialOffers,
        ),
        categories: NotificationCategories(
          priceAlerts: priceAlerts ?? _settings!.categories.priceAlerts,
          newProducts: newProducts ?? _settings!.categories.newProducts,
          stockAlerts: stockAlerts ?? _settings!.categories.stockAlerts,
        ),
        frequency: _settings!.frequency,
        stores: _settings!.stores,
      );
      
      // Save to secure storage first
      await _saveToSecureStorage(updatedSettings);
      
      // Construct the URL with user ID if available
      String endpoint = '$baseUrl/notifications';
      if (userId != null && userId.isNotEmpty) {
        endpoint = '$baseUrl/notifications/$userId';
        debugPrint('Using endpoint with user ID for update: $endpoint');
      } else {
        debugPrint('No user ID available for update, using default endpoint: $endpoint');
      }
      debugPrint('Notification settings update request: ${updatedSettings.toJson()}');

      final response = await http.put(
        Uri.parse(endpoint),
        headers: await _getHeaders(),
        body: jsonEncode(updatedSettings.toJson()),
      );

      debugPrint('Notification settings update response: ${response.body}');

      if (response.statusCode == 200) {
        _settings = updatedSettings;
        debugPrint('Notification settings updated successfully');
        return true;
      } else {
        _error = 'Failed to update notification settings: ${response.statusCode}';
        return false;
      }
    } catch (e) {
      _error = 'Error updating notification settings: $e';
      debugPrint(_error);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear settings (e.g., on logout)
  Future<void> clearSettings() async {
    try {
      await SecureStorageService.removeNotificationSettings();
      _settings = null;
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing notification settings: $e');
    }
  }
}
