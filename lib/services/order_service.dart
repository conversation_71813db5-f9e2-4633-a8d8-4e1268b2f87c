import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import '../models/cart.dart';
import '../models/product.dart';
import '../models/order_item.dart';
import '../services/auth_service.dart';
import '../models/user_model.dart';
import '../services/secure_storage_service.dart'; // Import SecureStorageService
import '../services/api_service.dart'; // Import ApiService

class OrderService with ChangeNotifier {
  static const String baseUrl = 'https://www.kemavuso.co.za/budget';
  static const String userKey = 'user_data';
  
  List<Order> _orders = [];
  bool _isLoading = false;
  String? _error;

  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get user ID from secure storage
  Future<String?> _getUserId() async {
    try {
      // Get user ID from secure storage
      final userId = await SecureStorageService.getUserId();
      if (userId == null || userId.isEmpty) {
        debugPrint('No user ID found in secure storage');
        return null;
      }
      debugPrint('Using user ID from secure storage: $userId');
      return userId;
    } catch (e) {
      debugPrint('Error getting user ID from secure storage: $e');
      return null;
    }
  }

  Future<void> fetchCurrentMonthOrders() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      debugPrint('Starting to fetch current month orders');
      final userId = await _getUserId();
      debugPrint('User ID from secure storage: $userId');
      
      if (userId == null) {
        // User is either not logged in or is anonymous/guest
        debugPrint('Cannot fetch orders: userId is null (user not logged in or is anonymous)');
        _error = 'Please log in to view your orders';
        _isLoading = false;
        _orders = []; // Empty the orders list for anonymous users
        notifyListeners();
        return;
      }

      debugPrint('Fetching orders for user: $userId');
      
      // Force refresh the token before making the API call
      final authService = AuthService();
      final tokenRefreshed = await authService.forceRefreshAndSaveToken();
      debugPrint('Token refresh result: $tokenRefreshed');
      
      // Check if token exists before making the request
      final token = await SecureStorageService.getFirebaseJwtToken();
      debugPrint('JWT Token before API call: ${token != null ? (token.length > 20 ? '${token.substring(0, 20)}...' : token) : 'null'}');
      
      final apiService = ApiService();
      final response = await apiService.get('/orders/user/$userId/current-month');

      debugPrint('Response status code: ${response.statusCode}');
      debugPrint('Response body length: ${response.body.length}');
      if (response.body.length < 1000) {
        debugPrint('Response body: ${response.body}');
      } else {
        debugPrint('Response body too large to print in full');
      }

      if (response.statusCode == 200) {
        try {
          final List<dynamic> ordersJson = jsonDecode(response.body);
          debugPrint('Successfully parsed JSON response with ${ordersJson.length} orders');
          
          _orders = [];
          for (var i = 0; i < ordersJson.length; i++) {
            try {
              final order = Order.fromJson(ordersJson[i]);
              _orders.add(order);
              debugPrint('Successfully parsed order ${i+1}/${ordersJson.length}');
            } catch (e) {
              debugPrint('Error parsing order ${i+1}: $e');
              debugPrint('Problematic JSON: ${ordersJson[i]}');
            }
          }
          
          _orders.sort((a, b) => b.orderDate.compareTo(a.orderDate)); // Most recent first
          debugPrint('Fetched and parsed ${_orders.length} orders');
        } catch (e) {
          debugPrint('Error parsing JSON response: $e');
          _error = 'Error parsing orders data';
        }
      } else if (response.statusCode == 404) {
        // 404 means no orders found for this user, which is a valid state
        debugPrint('No orders found for user (404 response)');
        _orders = []; // Set to empty list instead of setting an error
      } else {
        _error = 'Failed to fetch orders: ${response.statusCode}';
        debugPrint(_error);
        if (response.body.isNotEmpty) {
          debugPrint('Error response body: ${response.body}');
        }
      }
    } catch (e) {
      _error = 'Error fetching orders: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<Order?> placeOrder(Cart cart) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final userId = await _getUserId();
      
      if (userId == null) {
        // For guest/anonymous users, create a local order without making API calls
        debugPrint('Guest/anonymous user: Creating local order without API call');
        
        // Calculate totals
        final subtotal = cart.totalPrice;
        final vatAmount = subtotal * 0.15; // 15% VAT
        final totalQuantity = cart.items.fold(0, (sum, item) => sum + item.quantity);
        
        final order = Order(
          id: 'local_${DateTime.now().millisecondsSinceEpoch}',
          userId: 'guest',
          userEmail: 'guest@local',
          store: cart.selectedStore,
          items: cart.items.map((item) => OrderItem(
            id: item.id,
            name: item.name,
            price: item.price,
            quantity: item.quantity,
            imageUrl: item.imageUrl,
            barcode: item.searchBarcode,
            brand: item.brand,
            category: item.category,
            numericPrice: item.numericPrice,
            totalPrice: item.totalPrice,
          )).toList(),
          totalAmount: cart.totalPrice,
          orderDate: DateTime.now(),
          status: 'completed',
          paymentMethod: 'cash',
          subtotal: subtotal,
          vatAmount: vatAmount,
          totalQuantity: totalQuantity,
        );
        
        // Add to local orders list
        _orders.insert(0, order); // Add at the beginning
        _isLoading = false;
        notifyListeners();
        return order;
      }

      // For logged-in users, proceed with API call
      // Prepare order data
      final orderData = {
        'userId': userId,
        'store': cart.selectedStore,
        'items': cart.items.map((item) => {
          'productId': item.id,
          'name': item.name,
          'price': item.price,
          'quantity': item.quantity,
          'imageUrl': item.imageUrl,
        }).toList(),
        'totalAmount': cart.totalPrice,
        'orderDate': DateTime.now().toIso8601String(),
        'status': 'pending'
      };

      debugPrint('Placing order for user: $userId');
      final response = await http.post(
        Uri.parse('$baseUrl/orders'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(orderData),
      );

      if (response.statusCode == 201 || response.statusCode == 200) {
        final orderJson = jsonDecode(response.body);
        final order = Order.fromJson(orderJson);
        _orders.insert(0, order); // Add at the beginning
        notifyListeners();
        return order;
      } else {
        _error = 'Failed to place order: ${response.statusCode}';
        debugPrint(_error);
        return null;
      }
    } catch (e) {
      _error = 'Error placing order: $e';
      debugPrint(_error);
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> saveOrder(Cart cart, AuthService authService) async {
    try {
      // Check if user is anonymous/guest
      if (authService.user == null || authService.user!.isAnonymous) {
        debugPrint('User is anonymous/guest, creating local order without API call');
        
        // For anonymous users, create a local order without showing an error
        final now = DateTime.now();
        
        // Calculate VAT (15% in South Africa)
        final double vatRate = 0.15;
        final double subtotal = cart.totalPrice / (1 + vatRate);
        final double vatAmount = cart.totalPrice - subtotal;
        
        // Create a local order
        final order = Order(
          id: 'local_${now.millisecondsSinceEpoch}',
          userId: 'guest',
          userEmail: 'guest@local',
          store: cart.selectedStore,
          items: cart.items.map((item) => OrderItem(
            id: item.id,
            name: item.name,
            price: item.price,
            quantity: item.quantity,
            imageUrl: item.imageUrl,
            barcode: item.searchBarcode,
            brand: item.brand,
            category: item.category,
            numericPrice: item.numericPrice,
            totalPrice: item.totalPrice,
          )).toList(),
          totalAmount: cart.totalPrice,
          orderDate: now,
          status: 'completed',
          paymentMethod: 'cash',
          subtotal: subtotal,
          vatAmount: vatAmount,
          totalQuantity: cart.totalQuantity,
        );
        
        // Add to local orders list
        _orders.insert(0, order);
        notifyListeners();
        
        // Return true to indicate success for anonymous users
        return true;
      }

      // Get current time
      final now = DateTime.now();
      
      // Calculate VAT (15% in South Africa)
      final double vatRate = 0.15;
      final double subtotal = cart.totalPrice / (1 + vatRate);
      final double vatAmount = cart.totalPrice - subtotal;
      
      // Create unique order ID
      final String orderId = 'order_${now.millisecondsSinceEpoch}';
      
      // Convert store name to uppercase enum format
      final String storeEnum = cart.selectedStore.toUpperCase();
      
      // Get user ID from secure storage
      final userId = await _getUserId();
      if (userId == null) {
        debugPrint('Failed to get user ID from secure storage or user is anonymous/guest');
        _error = 'Please log in to save orders';
        notifyListeners();
        return false;
      }
      
      debugPrint('Using user ID from secure storage: $userId');
      
      // Force refresh the token before making the API call
      debugPrint('Forcing token refresh before saving order');
      final tokenRefreshed = await authService.forceRefreshAndSaveToken();
      debugPrint('Token refresh result: $tokenRefreshed');
      
      // Get the token for debugging
      final token = await SecureStorageService.getFirebaseJwtToken();
      debugPrint('JWT Token before API call: ${token != null ? (token.length > 20 ? '${token.substring(0, 20)}...' : token) : 'null'}');
      
      // Create order payload with proper JSON formatting
      final Map<String, dynamic> orderPayload = {
        "userId": userId,
        "userEmail": authService.user?.email ?? "",
        "orderDate": now.toIso8601String(),
        "store": storeEnum,
        "status": "COMPLETED",
        "paymentMethod": "CASH",
        "subtotal": subtotal,
        "vatAmount": vatAmount,
        "totalAmount": cart.totalPrice,
        "totalQuantity": cart.totalQuantity,
        "items": cart.items.map((product) => _convertProductToOrderItem(product)).toList(),
      };

      // Convert to JSON string and print for debugging
      final String jsonPayload = jsonEncode(orderPayload);
      debugPrint("Order payload (JSON): $jsonPayload");
      
      // Prepare headers with authorization token
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
      };
      
      // Add Authorization header if token is available
      if (token != null && token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
        debugPrint('Added Authorization header with Bearer token');
      } else {
        debugPrint('WARNING: No token available for Authorization header');
      }
      
      debugPrint('Create order API: \'$baseUrl/orders\'');
      
      // Make API call
      final response = await http.post(
        Uri.parse('$baseUrl/orders'),
        headers: headers,
        body: jsonPayload,
      );

      debugPrint("Response status: ${response.statusCode}");
      debugPrint("Response body: ${response.body}");

      // Check if successful
      if (response.statusCode == 200 || response.statusCode == 201) {
        debugPrint('Order saved successfully: $orderId');
        return true;
      } else {
        debugPrint('Error saving order. Status code: ${response.statusCode}');
        debugPrint('Response body: ${response.body}');
        _error = 'Failed to save order: ${response.statusCode}';
        notifyListeners();
        return false;
      }
    } catch (e) {
      debugPrint('Exception saving order: $e');
      _error = 'Error saving order: $e';
      notifyListeners();
      return false;
    }
  }
  
  // Helper method to convert Product to order item format
  Map<String, dynamic> _convertProductToOrderItem(Product product) {
    // Use a consistent placeholder URL for empty images
    final String placeholderUrl = "https://placehold.jp/150x150.png";
    
    // Ensure imageUrl has a value for display
    String imageUrl = product.imageUrl ?? "";
    if (imageUrl.isEmpty) {
      imageUrl = placeholderUrl;
    }
    
    // Store original image URL for reference only
    String originalImageUrl = product.originalImageUrl ?? "";
    if (originalImageUrl.isEmpty) {
      originalImageUrl = imageUrl; // Fallback to the display image if original is empty
    }
    
    return {
      "id": product.id,
      "store": product.store,
      "barcode": product.searchBarcode,
      "name": product.name,
      "brand": product.brand,
      "category": product.category,
      "price": product.numericPrice.toString(), // Use numeric price without currency symbol
      "numericPrice": product.numericPrice,
      "quantity": product.quantity,
      "totalPrice": product.totalPrice,
      "image_url": imageUrl,
      "originalImageUrl": originalImageUrl,
    };
  }
  
  // Get all orders for a user
  Future<List<Order>> getOrdersForUser(String userId) async {
    // Skip API call for anonymous/guest users
    if (userId == 'anonymous' || userId == 'guest') {
      debugPrint('User is anonymous/guest, not fetching orders');
      return [];
    }
    
    try {
      // Force refresh the token before making the API call
      debugPrint('Forcing token refresh before fetching orders for user: $userId');
      final authService = AuthService();
      final tokenRefreshed = await authService.forceRefreshAndSaveToken();
      debugPrint('Token refresh result: $tokenRefreshed');
      
      // Get the token for debugging
      final token = await SecureStorageService.getFirebaseJwtToken();
      debugPrint('JWT Token before API call: ${token != null ? (token.length > 20 ? '${token.substring(0, 20)}...' : token) : 'null'}');
      
      final apiService = ApiService();
      final response = await apiService.get('/orders/user/$userId');

      debugPrint('Get orders response status: ${response.statusCode}');
      if (response.body.length < 1000) {
        debugPrint('Response body: ${response.body}');
      } else {
        debugPrint('Response body too large to print in full');
      }

      if (response.statusCode == 200) {
        final List<dynamic> ordersJson = jsonDecode(response.body);
        return ordersJson.map((json) => Order.fromJson(json)).toList();
      } else if (response.statusCode == 404) {
        // 404 means no orders found, which is a valid state
        return [];
      } else {
        debugPrint('Failed to fetch orders: ${response.statusCode}');
        debugPrint('Error response body: ${response.body}');
        return [];
      }
    } catch (e) {
      debugPrint('Error fetching orders: $e');
      return [];
    }
  }

  // Get a specific order
  Future<Map<String, dynamic>?> getOrder(String orderId) async {
    try {
      // Force refresh the token before making the API call
      debugPrint('Forcing token refresh before fetching order: $orderId');
      final authService = AuthService();
      final tokenRefreshed = await authService.forceRefreshAndSaveToken();
      debugPrint('Token refresh result: $tokenRefreshed');
      
      // Get the token for debugging
      final token = await SecureStorageService.getFirebaseJwtToken();
      debugPrint('JWT Token before API call: ${token != null ? (token.length > 20 ? '${token.substring(0, 20)}...' : token) : 'null'}');
      
      final apiService = ApiService();
      final response = await apiService.get('/orders/$orderId');
      
      debugPrint('Get order response status: ${response.statusCode}');
      if (response.body.length < 1000) {
        debugPrint('Response body: ${response.body}');
      }
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> order = jsonDecode(response.body);
        return order;
      } else {
        debugPrint('Error getting order. Status code: ${response.statusCode}');
        debugPrint('Response body: ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Exception getting order: $e');
      return null;
    }
  }
  
  // Get current month's orders
  Future<List<Order>> getCurrentMonthOrders(String userId) async {
    // Skip API call for anonymous/guest users
    if (userId == 'anonymous' || userId == 'guest') {
      debugPrint('User is anonymous/guest, not fetching current month orders');
      return [];
    }
    
    try {
      // Force refresh the token before making the API call
      debugPrint('Forcing token refresh before fetching current month orders for user: $userId');
      final authService = AuthService();
      final tokenRefreshed = await authService.forceRefreshAndSaveToken();
      debugPrint('Token refresh result: $tokenRefreshed');
      
      // Get the token for debugging
      final token = await SecureStorageService.getFirebaseJwtToken();
      debugPrint('JWT Token before API call: ${token != null ? (token.length > 20 ? '${token.substring(0, 20)}...' : token) : 'null'}');
      
      final apiService = ApiService();
      final response = await apiService.get('/orders/user/$userId/current-month');
      
      debugPrint('Get current month orders response status: ${response.statusCode}');
      if (response.body.length < 1000) {
        debugPrint('Response body: ${response.body}');
      } else {
        debugPrint('Response body too large to print in full');
      }

      if (response.statusCode == 200) {
        final List<dynamic> ordersJson = jsonDecode(response.body);
        return ordersJson.map((json) => Order.fromJson(json)).toList();
      } else if (response.statusCode == 404) {
        // 404 means no orders found, which is a valid state
        return [];
      } else {
        debugPrint('Failed to fetch current month orders: ${response.statusCode}');
        debugPrint('Response body: ${response.body}');
        return [];
      }
    } catch (e) {
      debugPrint('Error fetching current month orders: $e');
      return [];
    }
  }
}

class Order {
  final String id;
  final String userId;
  final String userEmail;
  final DateTime orderDate;
  final String store;
  final String status;
  final String paymentMethod;
  final double subtotal;
  final double vatAmount;
  final double totalAmount;
  final int totalQuantity;
  final List<OrderItem> items;

  Order({
    required this.id,
    required this.userId,
    required this.userEmail,
    required this.orderDate,
    required this.store,
    required this.status,
    required this.paymentMethod,
    required this.subtotal,
    required this.vatAmount,
    required this.totalAmount,
    required this.totalQuantity,
    required this.items,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      userEmail: json['user_email'] ?? '',
      orderDate: DateTime.parse(json['order_date']),
      store: json['store'] ?? '',
      status: json['status'] ?? '',
      paymentMethod: json['payment_method'] ?? '',
      subtotal: (json['subtotal'] ?? 0).toDouble(),
      vatAmount: (json['vat_amount'] ?? 0).toDouble(),
      totalAmount: (json['total_amount'] ?? 0).toDouble(),
      totalQuantity: json['total_quantity'] ?? 0,
      items: (json['items'] as List<dynamic>)
          .map((item) => OrderItem.fromJson(item))
          .toList(),
    );
  }
}
