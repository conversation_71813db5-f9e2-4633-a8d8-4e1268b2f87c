import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'secure_storage_service.dart';

class MigrationService {
  // Migrate data from SharedPreferences to SecureStorage
  static Future<void> migrateToSecureStorage() async {
    try {
      debugPrint('Starting migration to secure storage');
      final prefs = await SharedPreferences.getInstance();
      
      // Migrate Firebase JWT token
      final token = prefs.getString('firebase_jwt_token');
      if (token != null && token.isNotEmpty) {
        debugPrint('Migrating Firebase JWT token to secure storage');
        await SecureStorageService.saveFirebaseJwtToken(token);
        await prefs.remove('firebase_jwt_token');
      }
      
      // Migrate user_id if it exists
      final userId = prefs.getString('user_id');
      if (userId != null && userId.isNotEmpty) {
        debugPrint('Migrating user ID to secure storage');
        await SecureStorageService.saveUserId(userId);
        await prefs.remove('user_id');
      }
      
      debugPrint('Migration to secure storage completed');
    } catch (e) {
      debugPrint('Error during migration to secure storage: $e');
    }
  }
}
