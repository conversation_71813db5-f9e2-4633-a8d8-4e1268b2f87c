import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

// Callback typedefs
typedef DeepLinkCallback = void Function(Uri uri);
typedef PaymentCallback = void Function(String path);

class DeepLinkService {
  // Singleton pattern
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  // Stream controller for deep links
  static final StreamController<Uri> _deepLinkStreamController = StreamController<Uri>.broadcast();
  static Stream<Uri> get deepLinkStream => _deepLinkStreamController.stream;

  // Callbacks
  static PaymentCallback? _paymentCallback;
  static Map<String, DeepLinkCallback> _routeCallbacks = {};

  // App scheme and host
  static const String appScheme = 'budgetbasket';
  static const String appHost = 'app';
  static const String webHost = 'budgetbasket.app';

  // Register callbacks
  static void setPaymentCallback(PaymentCallback callback) {
    _paymentCallback = callback;
  }

  static void registerRouteCallback(String route, DeepLinkCallback callback) {
    _routeCallbacks[route] = callback;
  }

  // Initialize deep links
  static Future<void> initDeepLinks() async {
    // This will be expanded in the future with platform channel listeners
    // for handling incoming links when the app is not running
    debugPrint('Deep link service initialized');
  }

  // Handle incoming deep links
  static void handleIncomingLink(Uri uri) {
    debugPrint('Received deep link: $uri');
    _deepLinkStreamController.add(uri);
    
    // Extract path and parameters
    final pathSegments = uri.pathSegments;
    if (pathSegments.isEmpty) return;
    
    final route = pathSegments.first;
    
    // Route to appropriate handler
    if (_routeCallbacks.containsKey(route)) {
      _routeCallbacks[route]!(uri);
    } else if (route == 'payment' && _paymentCallback != null) {
      _paymentCallback!(uri.toString());
    }
  }

  // Generate deep links
  static String generateDeepLink({
    required String route,
    Map<String, String>? parameters,
    bool useHttps = false,
  }) {
    final uriParameters = parameters ?? {};
    
    if (useHttps) {
      // Web URL format (for sharing)
      return Uri.https(webHost, '/$route', uriParameters).toString();
    } else {
      // Custom scheme format (for direct app opening)
      return Uri(
        scheme: appScheme,
        host: appHost,
        path: '/$route',
        queryParameters: uriParameters,
      ).toString();
    }
  }

  // Handle payment URLs
  static Future<void> handlePaymentUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  // Legacy method for backward compatibility
  static void handleDeepLink(String path) {
    if (_paymentCallback != null) {
      _paymentCallback!(path);
    }
  }
  
  // Share app via deep link
  static Future<void> shareApp() async {
    // Use the download page URL for sharing
    final uri = Uri.parse('https://budgetbasket.app/download');
    
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      debugPrint('Could not launch $uri');
    }
  }
}
