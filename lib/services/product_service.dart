import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import '../models/product.dart';
import 'secure_storage_service.dart';
import 'auth_service.dart';

class ProductService {
  // This endpoint requires JWT authentication
  static const String baseUrl = 'https://www.kemavuso.co.za/budget/search';
  // Fallback image URL for when the original image can't be loaded
  static const String fallbackImageUrl = 'https://via.placeholder.com/300x300?text=No+Image';

  Future<List<Product>> searchByBarcode(String barcode, String store) async {
    try {
      debugPrint('Searching for barcode: $barcode in store: $store');
      
      // Force refresh the token before making the API call
      debugPrint('Forcing token refresh before product search');
      final authService = AuthService();
      final tokenRefreshed = await authService.forceRefreshAndSaveToken();
      debugPrint('Token refresh result: $tokenRefreshed');
      
      // Get the token for the API call
      final token = await SecureStorageService.getFirebaseJwtToken();
      debugPrint('JWT Token before API call: ${token != null ? (token.length > 20 ? '${token.substring(0, 20)}...' : token) : 'null'}');
      
      // Prepare headers with authentication
      final headers = {
        'Content-Type': 'application/json',
      };
      
      // Add authorization header if we have a token
      if (token != null && token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
        debugPrint('Added Authorization header with token');
      } else {
        debugPrint('No token available for Authorization header');
      }
      
      final response = await http.post(
        Uri.parse(baseUrl),
        headers: headers,
        body: jsonEncode({
          'store': store,
          'barcode': barcode,
        }),
      );

      debugPrint('Response status code: ${response.statusCode}');
      if (response.body.length < 1000) {
        debugPrint('Response body: ${response.body}');
      } else {
        debugPrint('Response body too large to print in full');
      }

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        final List<dynamic> results = data['results'] ?? [];
        
        debugPrint('Found ${results.length} products');
        
        // Debug each product's data in detail
        for (var product in results) {
          debugPrint('Product: ${product['name']}');
          debugPrint('Price (raw): ${product['price']}');
          debugPrint('Price (type): ${product['price']?.runtimeType}');
          debugPrint('Price (toString): ${product['price'].toString()}');
          debugPrint('Image URL: ${product['image_url']}');
          
          // Dump the entire product JSON for debugging
          debugPrint('Full product data: $product');
        }
        
        final products = results.map((json) {
          // Get image URLs from the response
          String imageUrl = json['image_url'] ?? '';
          String originalImageUrl = json['originalImageUrl'] ?? json['image_url'] ?? '';
          
          // Always ensure imageUrl has a value for display
          if (imageUrl.contains('placeholder.com') || imageUrl.isEmpty) {
            imageUrl = 'https://placehold.jp/150x150.png'; // Use a proper placeholder
          }
          
          // Store original image URL for reference only
          if (originalImageUrl.isEmpty) {
            originalImageUrl = imageUrl; // Fallback to the display image if original is empty
          }
          
          // Ensure price is properly formatted
          var priceValue = json['price'];
          String price = '';
          
          debugPrint('Processing price for ${json['name']}: $priceValue (${priceValue?.runtimeType})');
          
          if (priceValue == null) {
            price = '0';
            debugPrint('Price is null, setting to 0');
          } else if (priceValue is String) {
            price = priceValue;
            debugPrint('Price is String: $price');
          } else if (priceValue is num) {
            price = priceValue.toString();
            debugPrint('Price is num: $price');
          } else {
            price = priceValue.toString();
            debugPrint('Price is other type: $price');
          }
          
          // If price is empty, set it to '0'
          if (price.isEmpty) {
            price = '0';
            debugPrint('Price was empty, setting to 0');
          }
          
          // If price doesn't start with 'R', add it
          if (!price.startsWith('R') && price.isNotEmpty) {
            price = 'R$price';
            debugPrint('Added R prefix: $price');
          }
          
          // Create the product
          final product = Product(
            searchBarcode: barcode,
            id: json['id'] ?? '',
            name: json['name'] ?? '',
            store: store,
            price: price,
            brand: json['brand'] ?? '',
            category: json['category'] ?? '',
            imageUrl: imageUrl,
            originalImageUrl: originalImageUrl,
            url: json['url'] ?? '',
          );
          
          debugPrint('Created product with price: ${product.price}, numericPrice: ${product.numericPrice}, formattedPrice: ${product.formattedPrice}');
          
          return product;
        }).toList();
        
        return products;
      } else {
        throw Exception('Failed to load products: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error searching by barcode: $e');
      return [];
    }
  }
}
