import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'dart:math' as math;
import 'secure_storage_service.dart';

class AuthService extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  User? _user;
  bool _isLoading = false;
  String _errorMessage = '';

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  bool get isLoggedIn => _user != null;
  String get errorMessage => _errorMessage;
  String? get email => _user?.email;
  String? get displayName => _user?.displayName;
  bool get isAnonymous => _user?.isAnonymous ?? false;

  // Get Firebase JWT token with auto-refresh if needed
  Future<String?> getFirebaseJwtToken() async {
    if (_user == null) return null;
    try {
      // Force refresh to ensure token is always fresh
      return await _user!.getIdToken(true);
    } catch (e) {
      debugPrint('Error getting Firebase JWT token: $e');
      return null;
    }
  }

  // Save Firebase JWT token to secure storage
  Future<bool> saveFirebaseJwtToken() async {
    if (_user == null) {
      debugPrint('Cannot save Firebase JWT token: user is null');
      return false;
    }
    try {
      final token = await getFirebaseJwtToken();
      if (token == null) {
        debugPrint('Cannot save Firebase JWT token: token is null');
        return false;
      }

      debugPrint(
          'Saving Firebase JWT token to secure storage: ${token.substring(0, 20)}...');
      final success = await SecureStorageService.saveFirebaseJwtToken(token);

      if (success) {
        debugPrint('Firebase JWT token saved successfully');
      } else {
        debugPrint('Failed to save Firebase JWT token');
      }

      // Verify the token was saved correctly
      final savedToken = await SecureStorageService.getFirebaseJwtToken();
      if (savedToken != null) {
        debugPrint(
            'Verified token in secure storage: ${savedToken.substring(0, 20)}...');
      } else {
        debugPrint(
            'WARNING: Token verification failed - could not retrieve saved token');
      }

      // Schedule next token refresh (tokens typically expire in 1 hour)
      Future.delayed(const Duration(minutes: 55), () {
        if (_user != null) {
          debugPrint('Auto-refreshing Firebase token');
          saveFirebaseJwtToken();
        }
      });

      return success;
    } catch (e) {
      debugPrint('Error saving Firebase JWT token: $e');
      return false;
    }
  }

  // Refresh token manually if needed
  Future<bool> refreshToken() async {
    debugPrint('Manually refreshing Firebase token');
    return saveFirebaseJwtToken();
  }

  // Force refresh and save the Firebase JWT token
  Future<bool> forceRefreshAndSaveToken() async {
    if (_user == null) {
      debugPrint('Cannot refresh token: user is null');
      return false;
    }

    try {
      debugPrint('Forcing token refresh for user: ${_user?.email}');

      // Force refresh the token
      final token = await _user!.getIdToken(true);
      if (token == null) {
        debugPrint('Failed to get fresh token from Firebase');
        return false;
      }

      debugPrint(
          'Got fresh token from Firebase: ${token.substring(0, math.min(token.length, 20))}...');

      // Save the refreshed token
      final success = await SecureStorageService.saveFirebaseJwtToken(token);

      if (success) {
        debugPrint('Refreshed token saved successfully');

        // Verify the token was saved
        final savedToken = await SecureStorageService.getFirebaseJwtToken();
        if (savedToken != null) {
          debugPrint(
              'Verified refreshed token in storage: ${savedToken.substring(0, math.min(savedToken.length, 20))}...');
          return true;
        } else {
          debugPrint(
              'WARNING: Token verification failed - could not retrieve saved token');
          return false;
        }
      } else {
        debugPrint('Failed to save refreshed token');
        return false;
      }
    } catch (e) {
      debugPrint('Error refreshing token: $e');
      return false;
    }
  }

  // Additional getters for compatibility
  String get userDisplayName {
    if (_user == null) return 'Guest';
    if (_user!.isAnonymous) return 'Guest';
    return _user!.displayName ?? 'User';
  }

  String get userEmail {
    if (_user == null || _user!.isAnonymous) return '';
    return _user!.email ?? '';
  }

  String get photoURL {
    if (_user == null || _user!.isAnonymous) return '';
    return _user!.photoURL ?? '';
  }

  bool get isAnonymousUser => _user?.isAnonymous ?? true;

  AuthService() {
    debugPrint('AuthService initialized');
    _isLoading = true;
    notifyListeners();

    // Get current user synchronously
    _user = _auth.currentUser;
    if (_user != null) {
      debugPrint('Found existing user: ${_user?.displayName}');
      _isLoading = false;
      notifyListeners();
    } else {
      // If no current user, set loading to false after a short delay
      // to prevent infinite loading state
      Future.delayed(const Duration(milliseconds: 500), () {
        if (_user == null) {
          debugPrint('No existing user found, setting loading to false');
          _isLoading = false;
          notifyListeners();
        }
      });
    }

    // Listen for auth state changes
    _auth.authStateChanges().listen((User? user) {
      debugPrint(
          'Auth state changed - Previous: ${_user?.displayName}, New: ${user?.displayName}');
      _user = user;
      _isLoading = false;
      notifyListeners();
    });
  }

  // Sign in with email and password
  Future<UserCredential?> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      _isLoading = true;
      _errorMessage = '';
      notifyListeners();

      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Save Firebase JWT token
      await saveFirebaseJwtToken();

      notifyListeners();
      return credential;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Google Sign-In
  Future<User?> signInWithGoogle() async {
    try {
      debugPrint('Starting Google Sign-In process');
      final GoogleSignInAccount? googleSignInAccount =
          await _googleSignIn.signIn();
      if (googleSignInAccount == null) {
        debugPrint('Google Sign-In cancelled by user');
        return null;
      }

      debugPrint(
          'Google Sign-In account obtained: ${googleSignInAccount.email}');
      final GoogleSignInAuthentication googleAuth =
          await googleSignInAccount.authentication;

      debugPrint('Google authentication obtained');
      final OAuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      debugPrint('Signing in with Firebase using Google credential');
      final UserCredential userCredential =
          await _auth.signInWithCredential(credential);
      _user = userCredential.user;
      debugPrint('Firebase user obtained: ${_user?.email}');

      // Save the JWT token to secure storage
      debugPrint('Attempting to save Firebase JWT token to secure storage');
      final tokenSaved = await saveFirebaseJwtToken();
      debugPrint('JWT token saved successfully: $tokenSaved');

      debugPrint('Google Sign-In successful for user: ${_user?.email}');
      notifyListeners();
      return _user;
    } catch (e) {
      _errorMessage = 'Google Sign-In failed: $e';
      debugPrint(_errorMessage);
      notifyListeners();
      return null;
    }
  }

  // Sign in anonymously (for guest users)
  Future<User?> signInAnonymously() async {
    try {
      _isLoading = true;
      _errorMessage = '';
      notifyListeners();

      debugPrint('Starting anonymous sign-in');

      final UserCredential userCredential = await _auth.signInAnonymously();
      _user = userCredential.user;

      // Save Firebase JWT token
      await saveFirebaseJwtToken();

      debugPrint('Anonymous sign-in successful: ${_user?.uid}');

      _isLoading = false;
      notifyListeners();
      return _user;
    } catch (e) {
      debugPrint('Error during anonymous sign-in: $e');
      _isLoading = false;
      _errorMessage = 'Failed to sign in anonymously: ${e.toString()}';
      notifyListeners();
      return null;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      _isLoading = true;
      _errorMessage = '';
      notifyListeners();

      // Sign out from Google if signed in with Google
      if (!(_user?.isAnonymous ?? true)) {
        debugPrint('Signing out from Google');
        await _googleSignIn.signOut();
      }

      // Clear all secure storage data
      debugPrint('Clearing all secure storage data');
      await SecureStorageService.deleteAll();
      debugPrint('Secure storage data cleared');

      // Sign out from Firebase
      debugPrint('Signing out from Firebase');
      await _auth.signOut();

      // Reset user
      _user = null;

      _isLoading = false;
      debugPrint('Sign out complete');
      notifyListeners();
    } catch (e) {
      debugPrint('Error during sign out: $e');
      _isLoading = false;
      _errorMessage = 'Failed to sign out: ${e.toString()}';
      notifyListeners();
    }
  }

  // Delete account
  Future<void> deleteAccount() async {
    try {
      _isLoading = true;
      notifyListeners();

      final user = _auth.currentUser;
      if (user != null) {
        // Re-authenticate if needed
        if (!user.isAnonymous) {
          final credential = await _googleSignIn.signIn();
          if (credential != null) {
            final auth = await credential.authentication;
            final googleCredential = GoogleAuthProvider.credential(
              accessToken: auth.accessToken,
              idToken: auth.idToken,
            );
            await user.reauthenticateWithCredential(googleCredential);
          }
        }

        // Delete the user account
        await user.delete();

        // Sign out after deletion
        await signOut();
      }
    } catch (e) {
      debugPrint('Error deleting account: $e');
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Request data download
  Future<void> requestDataDownload() async {
    try {
      _isLoading = true;
      notifyListeners();

      final user = _auth.currentUser;
      if (user != null && !user.isAnonymous && user.email != null) {
        // Here you would typically:
        // 1. Call your backend API to initiate the data export
        // 2. Send an email to the user with the data or a download link
        // For now, we'll just print a debug message
        debugPrint('Data download requested for user: ${user.email}');
      }
    } catch (e) {
      debugPrint('Error requesting data download: $e');
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
