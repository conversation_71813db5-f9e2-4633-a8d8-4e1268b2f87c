import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';

class AnalyticsService {
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer = FirebaseAnalyticsObserver(analytics: _analytics);

  // Screen tracking
  static Future<void> logScreenView({
    required String screenName,
    String? screenClass,
  }) async {
    if (kDebugMode) {
      print('📊 Screen View: $screenName');
    }
    await _analytics.logScreenView(
      screenName: screenName,
      screenClass: screenClass,
    );
  }

  // Developer Support Events
  static Future<void> logDeveloperSupport({
    required String action,
    required double amount,
    required bool success,
    String? errorMessage,
  }) async {
    if (kDebugMode) {
      print('📊 Developer Support: $action - Amount: $amount - Success: $success');
    }
    await _analytics.logEvent(
      name: 'developer_support',
      parameters: {
        'action': action,
        'amount': amount,
        'success': success,
        'error_message': errorMessage,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // User Actions
  static Future<void> logUserAction({
    required String action,
    Map<String, dynamic>? parameters,
  }) async {
    if (kDebugMode) {
      print('📊 User Action: $action ${parameters ?? ''}');
    }
    await _analytics.logEvent(
      name: action,
      parameters: parameters,
    );
  }

  // Error Events
  static Future<void> logError({
    required String error,
    required String description,
    StackTrace? stackTrace,
  }) async {
    if (kDebugMode) {
      print('📊 Error: $error - $description');
    }
    await _analytics.logEvent(
      name: 'app_error',
      parameters: {
        'error': error,
        'description': description,
        'stack_trace': stackTrace?.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // Profile Actions
  static Future<void> logProfileAction({
    required String action,
    Map<String, dynamic>? parameters,
  }) async {
    if (kDebugMode) {
      print('📊 Profile Action: $action ${parameters ?? ''}');
    }
    await _analytics.logEvent(
      name: 'profile_${action.toLowerCase()}',
      parameters: parameters,
    );
  }

  // Shopping Actions
  static Future<void> logShoppingAction({
    required String action,
    required String itemId,
    String? itemName,
    double? price,
    int? quantity,
  }) async {
    if (kDebugMode) {
      print('📊 Shopping Action: $action - Item: $itemName');
    }
    await _analytics.logEvent(
      name: action,
      parameters: {
        'item_id': itemId,
        'item_name': itemName,
        'price': price,
        'quantity': quantity,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // Budget Actions
  static Future<void> logBudgetAction({
    required String action,
    double? amount,
    String? store,
  }) async {
    if (kDebugMode) {
      print('📊 Budget Action: $action - Amount: $amount - Store: $store');
    }
    await _analytics.logEvent(
      name: 'budget_${action.toLowerCase()}',
      parameters: {
        'amount': amount,
        'store': store,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
}
