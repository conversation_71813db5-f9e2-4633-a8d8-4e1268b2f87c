import 'dart:convert';
import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/secure_storage_service.dart';
import '../services/api_service.dart';
import 'package:oktoast/oktoast.dart';

class UserService with ChangeNotifier {
  static const String baseUrl = 'https://www.kemavuso.co.za/budget';
  static const String userKey = 'user_data';

  User? _currentUser;
  bool _isLoading = false;
  String _errorMessage = '';

  // Getters
  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;

  // Fetch user data after login
  Future<bool> fetchUserData() async {
    final authService = AuthService();
    if (!authService.isLoggedIn) {
      _errorMessage = 'User not logged in';
      return false;
    }

    final email = authService.email;
    if (email == null || email.isEmpty) {
      _errorMessage = 'User email not available';
      return false;
    }

    _isLoading = true;
    notifyListeners();

    try {
      // First try to get the user by email
      print('Fetching user data for email: $email');

      final user = await getUserByEmail(email);

      if (user != null) {
        // User found, save to local storage
        print('User found, saving to local storage');
        _currentUser = user;
        await saveUserToLocal(user);
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        // User not found, create a new user
        print('User not found, creating new user');
        final displayName = authService.displayName ?? 'User';
        final success = await createUser(displayName, email);

        if (success) {
          // User created successfully and already saved in createUser method
          print('User creation and local storage successful');
          _isLoading = false;
          notifyListeners();
          return true;
        } else {
          // User creation failed
          print('User creation failed');
          _errorMessage = 'Failed to create user';
          _isLoading = false;
          notifyListeners();
          return false;
        }
      }
    } catch (e) {
      print('Error fetching user data: $e');
      _errorMessage = 'Error fetching user data: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Get user by email
  Future<User?> getUserByEmail(String email) async {
    try {
      print('Making API request to: $baseUrl/users/email/$email');

      final apiService = ApiService();
      final response = await apiService.get('/users/email/$email');

      print('Response status code: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200 && response.body.isNotEmpty) {
        final Map<String, dynamic> userData = jsonDecode(response.body);
        final user = User.fromJson(userData);

        // Save user to local storage
        await saveUserToLocal(user);

        return user;
      } else if (response.statusCode == 404) {
        print('User not found');
        return null;
      } else {
        print('Error getting user by email: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Exception getting user by email: $e');
      return null;
    }
  }

  // Save user to local storage
  Future<void> saveUserToLocal(User user) async {
    try {
      // Store only the user ID in secure storage
      await SecureStorageService.saveUserId(user.id);
      debugPrint('User ID saved to secure storage: ${user.id}');
    } catch (e) {
      debugPrint('Error saving user to secure storage: $e');
    }
  }

  // Get user from local storage
  Future<User?> getUserFromLocal() async {
    try {
      // Get user ID from secure storage
      final userId = await SecureStorageService.getUserId();

      if (userId != null && userId.isNotEmpty) {
        // If we have a user ID, we can fetch the full user data from the API if needed
        debugPrint('Found user ID in secure storage: $userId');
        // Create a minimal User object with the ID and required fields
        return User(
          id: userId,
          fullname: '',
          emailAddress: '',
          status: 'active',
          notificationSettings: NotificationPreferences(
            userId: userId,
            userEmail: '',
            lastUpdated: DateTime.now().toIso8601String(),
            preferences: {},
            categories: {},
            frequency: '',
            stores: [],
          ),
        );
      }

      return null;
    } catch (e) {
      debugPrint('Error getting user from secure storage: $e');
      return null;
    }
  }

  Future<String?> _getUserId() async {
    try {
      return await SecureStorageService.getUserId();
    } catch (e) {
      debugPrint('Error getting user ID from secure storage: $e');
      return null;
    }
  }

  // Create a new user
  Future<bool> createUser(String fullname, String email) async {
    try {
      final apiService = ApiService();
      final payload = {
        'fullname': fullname,
        'email_address': email,
        'status': 'active',
        'notification_settings': {
          'userId': null,
          'userEmail': email,
          'lastUpdated': DateTime.now().toIso8601String(),
          'preferences': {
            'pushNotifications': false,
            'emailNotifications': false,
            'specialOffers': false
          },
          'categories': {
            'priceAlerts': false,
            'newProducts': false,
            'stockAlerts': false
          },
          'frequency': 'daily',
          'stores': []
        }
      };

      final response = await apiService.post('/users', payload);

      print('Create user response status: ${response.statusCode}');
      print('Create user response body: ${response.body}');

      // HTTP 200 means success
      if (response.statusCode == 200 && response.body.isNotEmpty) {
        final Map<String, dynamic> userData = jsonDecode(response.body);
        final user = User.fromJson(userData);

        // Save user to local storage
        _currentUser = user;
        await saveUserToLocal(user);

        print('User created successfully with ID: ${user.id}');
        return true;
      } else {
        print('Error creating user: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('Exception creating user: $e');
      return false;
    }
  }

  // Update user profile
  Future<bool> updateUserProfile(String fullname, String email) async {
    try {
      _isLoading = true;
      notifyListeners();

      // Get current user
      final user = await getUserFromLocal();
      if (user == null) {
        _errorMessage = 'User not found in local storage';
        _isLoading = false;
        notifyListeners();
        return false;
      }

      final userId = await _getUserId();
      if (userId == null) {
        _errorMessage = 'User not logged in or anonymous';
        _isLoading = false;
        notifyListeners();
        return false;
      }

      // Create updated user
      final updatedUser = User(
        id: user.id,
        fullname: fullname,
        emailAddress: email,
        status: user.status,
        notificationSettings: user.notificationSettings,
      );

      final apiService = ApiService();
      // Send update to server
      final response =
          await apiService.put('/users/${user.id}', updatedUser.toJson());

      if (response.statusCode == 200) {
        // Update local storage
        _currentUser = updatedUser;
        await saveUserToLocal(updatedUser);

        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _errorMessage = 'Failed to update profile: ${response.statusCode}';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'Error updating profile: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Request data access
  Future<bool> requestDataAccess(String description) async {
    try {
      _isLoading = true;
      notifyListeners();

      debugPrint('Starting data access request process...');

      // Get user ID
      final user = await getUserFromLocal();
      if (user == null) {
        _errorMessage = 'User not found in local storage';
        debugPrint('Error: $_errorMessage');
        _isLoading = false;
        notifyListeners();

        // Show error toast
        _showToastMessage('Error: User not found', Colors.red);

        return false;
      }

      final userId = user.id;
      debugPrint('Requesting data access for user ID: $userId');

      if (userId.isEmpty) {
        _errorMessage = 'Invalid user ID';
        debugPrint('Error: $_errorMessage');
        _isLoading = false;
        notifyListeners();

        // Show error toast
        _showToastMessage('Error: Invalid user ID', Colors.red);

        return false;
      }

      // Prepare request payload
      final payload = {
        "request_type": "DATA_ACCESS",
        "description": description,
        "additional_data": {}
      };

      final apiService = ApiService();
      debugPrint(
          'Sending data access request with payload: ${jsonEncode(payload)}');
      debugPrint('Request URL: $baseUrl/users/$userId/requests');

      debugPrint('Data access request payload: ${jsonEncode(payload)}');

      // Send request to server
      final response =
          await apiService.post('/users/$userId/requests', payload);

      debugPrint('Data access request response status: ${response.statusCode}');
      debugPrint('Data access request response headers: ${response.headers}');

      if (response.body.isNotEmpty) {
        debugPrint('Response body: ${response.body}');
        try {
          final responseData = jsonDecode(response.body);
          debugPrint('Parsed response data: $responseData');
        } catch (e) {
          debugPrint('Failed to parse response body as JSON: $e');
        }
      }

      if (response.statusCode == 200) {
        debugPrint('Data access request successful');
        _isLoading = false;
        notifyListeners();

        // Show success toast
        _showToastMessage(
            'Data access request submitted successfully', Colors.green);

        return true;
      } else {
        _errorMessage = 'Failed to request data access: ${response.statusCode}';
        debugPrint('Error: $_errorMessage');
        _isLoading = false;
        notifyListeners();

        // Show error toast
        _showToastMessage('Failed to submit data access request', Colors.red);

        return false;
      }
    } catch (e) {
      _errorMessage = 'Error requesting data access: $e';
      debugPrint('Exception: $_errorMessage');
      _isLoading = false;
      notifyListeners();

      // Show error toast
      _showToastMessage('Error: $e', Colors.red);

      return false;
    }
  }

  // Request account deletion
  Future<bool> requestAccountDeletion(String reason) async {
    try {
      _isLoading = true;
      notifyListeners();

      debugPrint('Starting account deletion request process...');

      // Get user ID
      final user = await getUserFromLocal();
      if (user == null) {
        _errorMessage = 'User not found in local storage';
        debugPrint('Error: $_errorMessage');
        _isLoading = false;
        notifyListeners();

        // Show error toast
        _showToastMessage('Error: User not found', Colors.red);

        return false;
      }

      final userId = user.id;
      debugPrint('Requesting account deletion for user ID: $userId');

      if (userId.isEmpty) {
        _errorMessage = 'Invalid user ID';
        debugPrint('Error: $_errorMessage');
        _isLoading = false;
        notifyListeners();

        // Show error toast
        _showToastMessage('Error: Invalid user ID', Colors.red);

        return false;
      }

      // Prepare request payload
      final payload = {"reason": reason};

      final apiService = ApiService();
      debugPrint(
          'Sending account deletion request with payload: ${jsonEncode(payload)}');
      debugPrint('Request URL: $baseUrl/users/$userId/delete-account');

      // Send request to server using the dedicated endpoint
      final response =
          await apiService.post('/users/$userId/delete-account', payload);

      debugPrint(
          'Account deletion request response status: ${response.statusCode}');
      debugPrint(
          'Account deletion request response headers: ${response.headers}');

      if (response.body.isNotEmpty) {
        debugPrint('Response body: ${response.body}');
        try {
          final responseData = jsonDecode(response.body);
          debugPrint('Parsed response data: $responseData');
        } catch (e) {
          debugPrint('Failed to parse response body as JSON: $e');
        }
      }

      if (response.statusCode == 200 ||
          response.statusCode == 201 ||
          response.statusCode == 202) {
        debugPrint('Account deletion request successful');

        // Show success toast
        _showToastMessage(
            'Account deletion request submitted successfully', Colors.green);

        // Log out the user after successful account deletion request
        debugPrint(
            'Logging out user after successful account deletion request');
        final authService = AuthService();
        await authService.signOut();
        debugPrint('User logged out successfully');

        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _errorMessage =
            'Failed to request account deletion: ${response.statusCode}';
        debugPrint('Error: $_errorMessage');
        _isLoading = false;
        notifyListeners();

        // Show error toast
        _showToastMessage(
            'Account deletion request received and will be processed',
            Colors.red);

        return false;
      }
    } catch (e) {
      _errorMessage = 'Error requesting account deletion: $e';
      debugPrint('Exception: $_errorMessage');
      _isLoading = false;
      notifyListeners();

      // Show error toast
      _showToastMessage('Error: $e', Colors.red);

      return false;
    }
  }

  // Helper to show toast consistently
  void _showToastMessage(String message, Color bgColor) {
    showToast(
      message,
      duration: const Duration(seconds: 3),
      position: ToastPosition.bottom,
      backgroundColor: bgColor.withOpacity(0.9),
      textStyle: const TextStyle(color: Colors.white),
    );
  }
}
