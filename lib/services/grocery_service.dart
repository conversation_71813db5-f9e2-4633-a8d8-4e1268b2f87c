import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/grocery_product.dart';
import '../models/search_product.dart';
import '../models/category.dart';

class GroceryService {
  static const String baseUrl = 'https://www.kemavuso.co.za/budget';

  static Future<ProductsResponse?> fetchProducts({
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      // Use shoprite featured products endpoint for main products
      final url = Uri.parse(
          '$baseUrl/shoprite/featured?page=$page&page_size=$pageSize');

      print('Fetching products from: $url');

      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
      );

      print('Products response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        print('Products response data: ${jsonData.keys}');
        print('Number of products: ${jsonData['products']?.length ?? 0}');
        final result = ProductsResponse.fromJson(jsonData);
        print('Parsed ${result.products.length} products successfully');
        return result;
      } else {
        print('Failed to fetch products: ${response.statusCode}');
        print('Response body: ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error fetching products: $e');
      return null;
    }
  }

  static Future<List<GroceryProduct>> searchProducts({
    required String query,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      // Use the actual shoprite search endpoint
      final url = Uri.parse(
          '$baseUrl/shoprite/search?keyword=${Uri.encodeComponent(query)}&page=$page&page_size=$pageSize');

      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);

        // Convert the API response to GroceryProduct list
        final List<dynamic> products = jsonData['products'] ?? [];
        return products
            .map((product) => GroceryProduct.fromApiJson(product))
            .toList();
      }

      return [];
    } catch (e) {
      print('Error searching products: $e');
      return [];
    }
  }

  // Updated method to search using the correct shoprite endpoint
  static Future<SearchResponse?> searchProductsAcrossStores({
    required String keyword,
    String? store,
  }) async {
    try {
      // Use the actual shoprite search endpoint
      final url =
          '$baseUrl/shoprite/search?keyword=${Uri.encodeComponent(keyword)}';

      print('Searching with URL: $url'); // Debug log

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
        },
      );

      print('Search response status: ${response.statusCode}'); // Debug log
      print('Search response body: ${response.body}'); // Debug log

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);

        // Convert the shoprite API response to our SearchResponse format
        final List<dynamic> products = jsonData['products'] ?? [];
        final List<GroceryProduct> groceryProducts = products
            .map((product) => GroceryProduct.fromApiJson(product))
            .toList();

        // Create a SearchResponse with shoprite store data
        final storeProducts = StoreProducts(
          products: groceryProducts,
          count: jsonData['total_count'] ?? products.length,
          hasNext: jsonData['has_next'] ?? false,
        );

        return SearchResponse(
          keyword: keyword,
          stores: {'shoprite': storeProducts},
          totalProducts: groceryProducts.length,
        );
      } else {
        print('Failed to search products: ${response.statusCode}');
        print('Response body: ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error searching products: $e');
      return null;
    }
  }

  // Method to search multiple stores (for now just shoprite)
  static Future<SearchResponse?> searchMultipleStores({
    required String keyword,
  }) async {
    try {
      // For now, we only have shoprite endpoint
      return await searchProductsAcrossStores(keyword: keyword);
    } catch (e) {
      print('Error searching multiple stores: $e');
      return null;
    }
  }

  static Future<CategoriesResponse?> fetchCategories() async {
    try {
      final url = Uri.parse('$baseUrl/shoprite/categories');

      print('Fetching categories from: $url');

      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
      );

      print('Categories response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        print('Categories response data: ${jsonData.keys}');
        print('Number of categories: ${jsonData['categories']?.length ?? 0}');
        final result = CategoriesResponse.fromJson(jsonData);
        print('Parsed ${result.categories.length} categories successfully');
        return result;
      } else {
        print('Failed to fetch categories: ${response.statusCode}');
        print('Response body: ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error fetching categories: $e');
      return null;
    }
  }

  static Future<List<GroceryProduct>> searchProductsByCategory({
    required String category,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      // Use the shoprite search endpoint with keyword parameter instead of category
      // since the API expects keyword searches
      final url = Uri.parse(
          '$baseUrl/shoprite/search?keyword=${Uri.encodeComponent(category)}&page=$page&page_size=$pageSize');

      print('Searching products by category: $category');
      print('Search URL: $url');

      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
      );

      print('Category search response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        print('Category search response data: ${jsonData.keys}');

        // Convert the API response to GroceryProduct list
        final List<dynamic> products = jsonData['products'] ?? [];
        print('Found ${products.length} products for category: $category');
        final result = products
            .map((product) => GroceryProduct.fromApiJson(product))
            .toList();
        print('Successfully parsed ${result.length} products');
        return result;
      } else {
        print('Failed to search products by category: ${response.statusCode}');
        print('Response body: ${response.body}');
      }

      return [];
    } catch (e) {
      print('Error searching products by category: $e');
      return [];
    }
  }
}
