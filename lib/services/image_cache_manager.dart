import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class ImageCacheManager {
  // In-memory cache for image data
  static final Map<String, Uint8List> _imageDataCache = {};
  static final Map<String, Future<Uint8List>> _pendingDownloads = {};
  
  // Default fallback image data - a 1x1 transparent pixel PNG
  static final Uint8List _defaultFallbackImageData = Uint8List.fromList([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
    0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
    0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
    0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
  ]); // 1x1 transparent PNG

  // Get image data from cache or download
  static Future<Uint8List> getImageData(String imageUrl) async {
    // Handle empty, invalid URLs, or placeholder URLs
    if (imageUrl.isEmpty || 
        imageUrl == 'null' || 
        imageUrl == 'undefined' ||
        imageUrl.contains('placeholder.com')) {
      return _defaultFallbackImageData;
    }
    
    // Return from memory cache if available
    if (_imageDataCache.containsKey(imageUrl)) {
      return _imageDataCache[imageUrl]!;
    }

    // If a download is already in progress, return that future
    if (_pendingDownloads.containsKey(imageUrl)) {
      return _pendingDownloads[imageUrl]!;
    }

    // Start a new download
    final Future<Uint8List> downloadFuture = _downloadImage(imageUrl);
    _pendingDownloads[imageUrl] = downloadFuture;
    
    try {
      final imageData = await downloadFuture;
      // Store in cache and remove from pending
      _imageDataCache[imageUrl] = imageData;
      _pendingDownloads.remove(imageUrl);
      return imageData;
    } catch (e) {
      // Clean up on error
      _pendingDownloads.remove(imageUrl);
      debugPrint("Error getting image data for $imageUrl: $e");
      return _defaultFallbackImageData;
    }
  }

  // Download image data
  static Future<Uint8List> _downloadImage(String imageUrl) async {
    try {
      debugPrint("Downloading image: $imageUrl");
      
      // Set timeout to avoid hanging
      final dio = Dio();
      final response = await dio.get(
        imageUrl,
        options: Options(
          responseType: ResponseType.bytes,
          headers: {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
          },
          receiveTimeout: const Duration(seconds: 10),
          sendTimeout: const Duration(seconds: 10),
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        return response.data;
      } else {
        throw Exception("Failed to download image: ${response.statusCode}");
      }
    } catch (e) {
      debugPrint("Error downloading image: $e");
      rethrow;
    }
  }

  // Clear the entire cache
  static void clearCache() {
    _imageDataCache.clear();
    _pendingDownloads.clear();
  }

  // Get cache size
  static int getCacheSize() {
    int totalSize = 0;
    _imageDataCache.forEach((_, data) {
      totalSize += data.length;
    });
    return totalSize;
  }
}
