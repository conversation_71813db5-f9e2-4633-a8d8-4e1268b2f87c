import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PermissionService extends ChangeNotifier {
  bool _hasShownPrivacyDisclaimer = false;
  bool _locationPermissionGranted = false;
  bool _notificationPermissionGranted = false;
  bool _cameraPermissionGranted = false;
  bool _microphonePermissionGranted = false;
  
  bool get hasShownPrivacyDisclaimer => _hasShownPrivacyDisclaimer;
  bool get locationPermissionGranted => _locationPermissionGranted;
  bool get notificationPermissionGranted => _notificationPermissionGranted;
  bool get cameraPermissionGranted => _cameraPermissionGranted;
  bool get microphonePermissionGranted => _microphonePermissionGranted;
  
  PermissionService() {
    _loadPermissionStatus();
  }
  
  Future<void> _loadPermissionStatus() async {
    final prefs = await SharedPreferences.getInstance();
    _hasShownPrivacyDisclaimer = prefs.getBool('hasShownPrivacyDisclaimer') ?? false;
    
    // Check current permission status
    _locationPermissionGranted = await _checkLocationPermission();
    _notificationPermissionGranted = await _checkNotificationPermission();
    _cameraPermissionGranted = await _checkCameraPermission();
    _microphonePermissionGranted = await _checkMicrophonePermission();
    
    notifyListeners();
  }
  
  Future<bool> _checkLocationPermission() async {
    return await Permission.locationWhenInUse.isGranted;
  }
  
  Future<bool> _checkNotificationPermission() async {
    return await Permission.notification.isGranted;
  }

  Future<bool> _checkCameraPermission() async {
    return await Permission.camera.isGranted;
  }

  Future<bool> _checkMicrophonePermission() async {
    return await Permission.microphone.isGranted;
  }
  
  Future<bool> requestLocationPermission() async {
    final status = await Permission.location.request();
    _locationPermissionGranted = status.isGranted;
    notifyListeners();
    return _locationPermissionGranted;
  }
  
  Future<bool> requestNotificationPermission() async {
    final status = await Permission.notification.request();
    _notificationPermissionGranted = status.isGranted;
    notifyListeners();
    return _notificationPermissionGranted;
  }

  Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    _cameraPermissionGranted = status.isGranted;
    notifyListeners();
    return _cameraPermissionGranted;
  }

  Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    _microphonePermissionGranted = status.isGranted;
    notifyListeners();
    return _microphonePermissionGranted;
  }
  
  Future<void> markPrivacyDisclaimerAsShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('hasShownPrivacyDisclaimer', true);
    _hasShownPrivacyDisclaimer = true;
    notifyListeners();
  }
  
  // Get the current position (for approximate location)
  Future<Position?> getCurrentPosition() async {
    if (!_locationPermissionGranted) {
      return null;
    }
    
    try {
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.low, // Use low accuracy for approximate location
      );
    } catch (e) {
      debugPrint('Error getting location: $e');
      return null;
    }
  }
}
