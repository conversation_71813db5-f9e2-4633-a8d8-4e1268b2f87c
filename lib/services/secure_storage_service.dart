import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorageService {
  static const String _firebaseJwtKey = 'firebase_jwt_token';
  static const String _userIdKey = 'user_id';
  static const String _userDataKey = 'user_data';
  static const String _notificationSettingsKey = 'notification_settings';
  
  static final FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: const AndroidOptions(
      encryptedSharedPreferences: true,
    ),
  );

  // Save Firebase JWT token
  static Future<bool> saveFirebaseJwtToken(String token) async {
    try {
      await _secureStorage.write(key: _firebaseJwtKey, value: token);
      debugPrint('Firebase JWT token saved to secure storage');
      return true;
    } catch (e) {
      debugPrint('Error saving Firebase JWT token to secure storage: $e');
      return false;
    }
  }

  // Get Firebase JWT token
  static Future<String?> getFirebaseJwtToken() async {
    try {
      return await _secureStorage.read(key: _firebaseJwtKey);
    } catch (e) {
      debugPrint('Error getting Firebase JWT token from secure storage: $e');
      return null;
    }
  }

  // Delete Firebase JWT token
  static Future<bool> deleteFirebaseJwtToken() async {
    try {
      await _secureStorage.delete(key: _firebaseJwtKey);
      return true;
    } catch (e) {
      debugPrint('Error deleting Firebase JWT token from secure storage: $e');
      return false;
    }
  }

  // Save user ID
  static Future<bool> saveUserId(String userId) async {
    try {
      await _secureStorage.write(key: _userIdKey, value: userId);
      return true;
    } catch (e) {
      debugPrint('Error saving user ID to secure storage: $e');
      return false;
    }
  }

  // Get user ID
  static Future<String?> getUserId() async {
    try {
      return await _secureStorage.read(key: _userIdKey);
    } catch (e) {
      debugPrint('Error getting user ID from secure storage: $e');
      return null;
    }
  }

  // Delete user ID
  static Future<bool> deleteUserId() async {
    try {
      await _secureStorage.delete(key: _userIdKey);
      return true;
    } catch (e) {
      debugPrint('Error deleting user ID from secure storage: $e');
      return false;
    }
  }

  // Get user data
  static Future<String?> getUserData() async {
    try {
      return await _secureStorage.read(key: _userDataKey);
    } catch (e) {
      debugPrint('Error getting user data from secure storage: $e');
      return null;
    }
  }

  // Save user data
  static Future<bool> saveUserData(String userData) async {
    try {
      await _secureStorage.write(key: _userDataKey, value: userData);
      return true;
    } catch (e) {
      debugPrint('Error saving user data to secure storage: $e');
      return false;
    }
  }

  // Save notification settings
  static Future<bool> saveNotificationSettings(String settings) async {
    try {
      await _secureStorage.write(key: _notificationSettingsKey, value: settings);
      debugPrint('Notification settings saved to secure storage');
      return true;
    } catch (e) {
      debugPrint('Error saving notification settings to secure storage: $e');
      return false;
    }
  }

  // Get notification settings
  static Future<String?> getNotificationSettings() async {
    try {
      return await _secureStorage.read(key: _notificationSettingsKey);
    } catch (e) {
      debugPrint('Error getting notification settings from secure storage: $e');
      return null;
    }
  }

  // Remove notification settings
  static Future<bool> removeNotificationSettings() async {
    try {
      await _secureStorage.delete(key: _notificationSettingsKey);
      debugPrint('Notification settings removed from secure storage');
      return true;
    } catch (e) {
      debugPrint('Error removing notification settings from secure storage: $e');
      return false;
    }
  }

  // Delete all secure storage data
  static Future<bool> deleteAll() async {
    try {
      await _secureStorage.deleteAll();
      return true;
    } catch (e) {
      debugPrint('Error deleting all secure storage data: $e');
      return false;
    }
  }
}
