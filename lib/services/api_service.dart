import 'dart:convert';
import 'dart:math' as math;
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'secure_storage_service.dart';

class ApiService {
  static const String baseUrl = 'https://www.kemavuso.co.za/budget';
  
  Future<String?> _getToken() async {
    try {
      return await SecureStorageService.getFirebaseJwtToken();
    } catch (e) {
      debugPrint('Error getting token from secure storage: $e');
      return null;
    }
  }

  Future<Map<String, String>> _getHeaders() async {
    final token = await _getToken();
    if (token == null || token.isEmpty) {
      debugPrint('No token found in secure storage');
      // Instead of throwing an exception, return headers without Authorization
      debugPrint('Returning headers without Authorization token');
      return {
        'Content-Type': 'application/json',
      };
    }
    
    debugPrint('Retrieved token from secure storage: ${token.substring(0, math.min(token.length, 20))}...');
    
    if (!_isValidJwt(token)) {
      debugPrint('Invalid JWT token format');
      // Instead of throwing an exception, return headers without Authorization
      return {
        'Content-Type': 'application/json',
      };
    }
    
    debugPrint('Adding Authorization header with Bearer token');
    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }

  // Validate JWT token format
  bool _isValidJwt(String token) {
    // JWT should have 3 parts separated by dots
    final parts = token.split('.');
    if (parts.length != 3) {
      debugPrint('Invalid JWT structure');
      return false;
    }
    
    // Each part should be valid base64
    try {
      parts.forEach((part) {
        // Convert URL-safe base64 to standard base64
        final converted = part.replaceAll('-', '+').replaceAll('_', '/');
        // Add padding if needed
        final padded = converted.padRight(converted.length + (4 - converted.length % 4) % 4, '=');
        base64Decode(padded);
      });
      return true;
    } catch (e) {
      debugPrint('Invalid JWT base64 encoding: $e');
      return false;
    }
  }

  Future<http.Response> get(String endpoint) async {
    try {
      final headers = await _getHeaders();
      debugPrint('Making GET request to: $baseUrl$endpoint');
      debugPrint('Headers: ${headers.toString()}');
      
      final response = await http.get(
        Uri.parse('$baseUrl$endpoint'),
        headers: headers,
      );
      
      if (response.statusCode == 401 || response.statusCode == 403) {
        debugPrint('Authentication error: ${response.statusCode}');
        debugPrint('Response body: ${response.body}');
        // Try to refresh token if authentication fails
        await _refreshToken();
      }
      
      return response;
    } catch (e) {
      debugPrint('Error in GET request: $e');
      rethrow;
    }
  }

  Future<void> _refreshToken() async {
    try {
      debugPrint('Attempting to refresh token...');
      // Add token refresh logic here if needed
    } catch (e) {
      debugPrint('Error refreshing token: $e');
    }
  }

  Future<http.Response> post(String endpoint, dynamic body) async {
    final headers = await _getHeaders();
    final response = await http.post(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
      body: body is String ? body : jsonEncode(body),
    );
    return response;
  }

  Future<http.Response> put(String endpoint, dynamic body) async {
    final headers = await _getHeaders();
    final response = await http.put(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
      body: body is String ? body : jsonEncode(body),
    );
    return response;
  }

  Future<http.Response> delete(String endpoint) async {
    final headers = await _getHeaders();
    final response = await http.delete(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
    );
    return response;
  }
}
