import 'package:shared_preferences/shared_preferences.dart';

class PreferencesService {
  static const String _privacyAcceptedKey = 'privacy_accepted';
  static const String _locationAcceptedKey = 'location_accepted';

  static Future<bool> hasAcceptedPrivacy() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_privacyAcceptedKey) ?? false;
  }

  static Future<bool> hasAcceptedLocation() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_locationAcceptedKey) ?? false;
  }

  static Future<void> setPrivacyAccepted(bool accepted) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_privacyAcceptedKey, accepted);
  }

  static Future<void> setLocationAccepted(bool accepted) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_locationAcceptedKey, accepted);
  }

  static Future<void> clearAllPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }
}
