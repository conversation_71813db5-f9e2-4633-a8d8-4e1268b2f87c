import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/cart.dart';
import '../models/product.dart';
import '../models/grocery_product.dart';

import '../services/grocery_service.dart';
import '../utils/category_utils.dart';
import '../widgets/cart_badge.dart';
import 'cart_screen.dart';
import 'dart:async';

class SearchScreen extends StatefulWidget {
  const SearchScreen({Key? key}) : super(key: key);

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  Timer? _searchDebounce;

  List<GroceryProduct> _searchResults = [];
  List<CategoryGroup> _categoryGroups = [];
  bool _isLoading = false;
  bool _showingResults = false;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadCategories();
    // Auto-focus search bar when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _searchDebounce?.cancel();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final categoriesResponse = await GroceryService.fetchCategories();
      final categories = categoriesResponse?.categories ?? [];
      final nonEmptyCategories =
          categories.where((c) => c.productCount > 0).toList();
      final grouped = CategoryGroup.groupCategories(nonEmptyCategories);

      setState(() {
        _categoryGroups = grouped;
      });
    } catch (e) {
      debugPrint('Error loading categories: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _showingResults = false;
        _searchResults = [];
        _searchQuery = '';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _searchQuery = query;
      _showingResults = true;
    });

    try {
      final products =
          await GroceryService.searchProductsByCategory(category: query);
      setState(() {
        _searchResults = products;
      });
    } catch (e) {
      debugPrint('Error searching: $e');
      setState(() {
        _searchResults = [];
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _onSearchChanged(String query) {
    _searchDebounce?.cancel();
    _searchDebounce = Timer(const Duration(milliseconds: 500), () {
      _performSearch(query);
    });
  }

  void _onCategorySelected(String categoryName) {
    _searchController.text = categoryName;
    _performSearch(categoryName);
  }

  void _addToCart(GroceryProduct product) {
    final cart = Provider.of<Cart>(context, listen: false);
    final cartProduct = Product(
        id: product.id,
        name: product.name,
        store: product.shopName,
        price: product.formattedPrice,
        imageUrl: product.primaryImage,
        category: product.primaryCategory,
        searchBarcode: '',
        brand: '',
        originalImageUrl: product.primaryImage,
        quantity: 1,
        url: '');
    cart.addProduct(cartProduct);
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text('${product.name} added to cart'),
      backgroundColor: Colors.green,
      duration: const Duration(seconds: 2),
    ));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: _searchController,
          focusNode: _searchFocusNode,
          onChanged: _onSearchChanged,
          onSubmitted: _performSearch,
          decoration: InputDecoration(
            hintText: "Search for products...",
            border: InputBorder.none,
            hintStyle: theme.textTheme.bodyLarge?.copyWith(color: Colors.grey),
          ),
          style: theme.textTheme.bodyLarge,
        ),
        actions: [
          Consumer<Cart>(
            builder: (context, cart, child) => CartBadge(
              child: IconButton(
                icon: const Icon(Icons.shopping_cart_outlined),
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const CartScreen()),
                ),
              ),
            ),
          ),
        ],
        backgroundColor: theme.scaffoldBackgroundColor,
        elevation: 1,
      ),
      body: _showingResults ? _buildSearchResults() : _buildCategoriesView(),
    );
  }

  Widget _buildSearchResults() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No products found for "$_searchQuery"',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Try searching with different keywords',
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 16,
        crossAxisSpacing: 16,
        childAspectRatio: 0.7,
      ),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        return _buildProductCard(_searchResults[index]);
      },
    );
  }

  Widget _buildCategoriesView() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Text(
          'Browse Categories',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        ..._categoryGroups.map((group) => _buildCategoryGroup(group)),
      ],
    );
  }

  Widget _buildCategoryGroup(CategoryGroup group) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        leading: Text(group.icon, style: const TextStyle(fontSize: 24)),
        title: Text(group.name),
        subtitle: Text('${group.categories.length} categories'),
        children: group.categories
            .map((category) => ListTile(
                  leading: const Icon(Icons.category),
                  title: Text(category.displayName),
                  subtitle: Text('${category.productCount} products'),
                  onTap: () => _onCategorySelected(category.topLevelCategory),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildProductCard(GroceryProduct product) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 5,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Stack(
                children: [
                  Positioned.fill(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: product.primaryImage.isNotEmpty
                          ? Image.network(
                              product.primaryImage,
                              fit: BoxFit.cover,
                              headers: {
                                'User-Agent':
                                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                'Accept':
                                    'image/webp,image/apng,image/*,*/*;q=0.8',
                                'Referer': 'https://www.shoprite.co.za/',
                              },
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                color: Colors.grey[200],
                                child: Icon(Icons.shopping_basket_outlined,
                                    color: Colors.grey[400]),
                              ),
                            )
                          : Container(
                              color: Colors.grey[200],
                              child: Icon(Icons.shopping_basket_outlined,
                                  color: Colors.grey[400]),
                            ),
                    ),
                  ),
                  if (product.hasDiscount)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '${(product.discount * 100).toInt()}% OFF',
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    product.name,
                    style: theme.textTheme.bodyMedium
                        ?.copyWith(fontWeight: FontWeight.w600),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (product.hasDiscount)
                            Text(
                              product.formattedPrice,
                              style: theme.textTheme.bodySmall?.copyWith(
                                decoration: TextDecoration.lineThrough,
                                color: Colors.grey,
                              ),
                            ),
                          Text(
                            product.discountedPrice,
                            style: theme.textTheme.titleSmall?.copyWith(
                              color: theme.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      InkWell(
                        onTap: () => _addToCart(product),
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: theme.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(Icons.add,
                              color: Colors.white, size: 16),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
