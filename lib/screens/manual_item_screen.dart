import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/cart.dart';
import '../models/product.dart';
import '../providers/theme_provider.dart';
import '../widgets/cart_badge.dart';
import 'cart_screen.dart';

class ManualItemScreen extends StatefulWidget {
  final String? initialBarcode;
  final String? shop;
  
  const ManualItemScreen({
    Key? key,
    this.initialBarcode,
    this.shop,
  }) : super(key: key);

  @override
  State<ManualItemScreen> createState() => _ManualItemScreenState();
}

class _ManualItemScreenState extends State<ManualItemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _priceController = TextEditingController();
  final _quantityController = TextEditingController(text: '1');
  final _barcodeController = TextEditingController();
  
  bool get isDefaultStore => widget.shop == null || widget.shop == 'Default';
  
  @override
  void initState() {
    super.initState();
    
    // Set initial barcode if provided
    if (widget.initialBarcode != null && widget.initialBarcode!.isNotEmpty) {
      _barcodeController.text = widget.initialBarcode!;
    }
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    _quantityController.dispose();
    _barcodeController.dispose();
    super.dispose();
  }
  
  void _addItemToCart() {
    if (_formKey.currentState!.validate()) {
      final name = _nameController.text;
      final price = _priceController.text;
      final quantity = int.parse(_quantityController.text);
      
      // Create a product object
      final product = Product(
        id: 'manual_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        price: price,
        searchBarcode: _barcodeController.text.isEmpty ? 'custom-${DateTime.now().millisecondsSinceEpoch}' : _barcodeController.text,
        brand: 'Custom',
        category: 'Custom',
        imageUrl: 'https://placehold.jp/150x150.png',
        originalImageUrl: 'https://placehold.jp/150x150.png',
        url: '',
        store: isDefaultStore ? 'Default' : widget.shop!,
        quantity: quantity,
      );
      
      // Add to cart
      final cart = Provider.of<Cart>(context, listen: false);
      final bool success = cart.addProduct(product);
      
      if (success) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Item added to cart'),
            duration: Duration(seconds: 2),
          ),
        );
        
        // Navigate back to previous screen
        Navigator.pop(context);
      } else {
        // Show error message
        String errorMessage = '';
        if (cart.hasReachedMaxItems) {
          errorMessage = 'Cannot add item. Maximum cart limit of ${Cart.maxItemCount} items reached.';
        } else if (cart.hasReachedMaxAmount) {
          errorMessage = 'Cannot add item. Maximum cart amount of R${Cart.maxTotalAmount.toStringAsFixed(2)} reached.';
        } else {
          errorMessage = 'Cannot add item to cart. Cart limits reached.';
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Manual Item'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white, // Always white text for top bar
        actions: [
          CartBadge(
            badgeColor: Colors.red,
            child: IconButton(
              icon: const Icon(Icons.shopping_cart),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CartScreen(),
                  ),
                );
              },
              tooltip: 'Cart',
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Info card
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Theme.of(context).primaryColor,
                          size: 32,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Add Custom Items',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Use this form to add items from shops that we do not support yet or products which we could not find using a barcode. Enter the item name, price, and quantity to add it to your cart.',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Item details
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Item Details',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 20),
                    
                    // Barcode field (optional)
                    TextFormField(
                      controller: _barcodeController,
                      decoration: InputDecoration(
                        labelText: 'Barcode (Optional)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        prefixIcon: const Icon(Icons.qr_code),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    
                    // Name field
                    TextFormField(
                      controller: _nameController,
                      decoration: InputDecoration(
                        labelText: 'Item Name',
                        hintText: 'Enter the item name',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.shopping_bag_outlined),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter an item name';
                        }
                        return null;
                      },
                      textCapitalization: TextCapitalization.words,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Price
                    TextFormField(
                      controller: _priceController,
                      decoration: InputDecoration(
                        labelText: 'Price (R)',
                        hintText: 'Enter the price',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.attach_money),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a price';
                        }
                        try {
                          final price = double.parse(value);
                          if (price <= 0) {
                            return 'Price must be greater than zero';
                          }
                        } catch (e) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Quantity
                    TextFormField(
                      controller: _quantityController,
                      decoration: InputDecoration(
                        labelText: 'Quantity',
                        hintText: 'Enter the quantity',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.format_list_numbered),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a quantity';
                        }
                        try {
                          final quantity = int.parse(value);
                          if (quantity <= 0) {
                            return 'Quantity must be greater than zero';
                          }
                        } catch (e) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Add to cart button
                    ElevatedButton.icon(
                      onPressed: _addItemToCart,
                      icon: const Icon(Icons.add_shopping_cart),
                      label: const Text('ADD TO CART'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                    
                    // Continue scanning button (only if not default store)
                    if (!isDefaultStore) ...[
                      const SizedBox(height: 16),
                      OutlinedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: const Icon(Icons.camera_alt),
                        label: const Text('CONTINUE SCANNING'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Theme.of(context).primaryColor,
                          side: BorderSide(color: Theme.of(context).primaryColor),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                    
                    // Add extra space at the bottom for scrolling
                    const SizedBox(height: 20),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
