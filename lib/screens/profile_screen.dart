import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../services/auth_service.dart';
import '../services/user_service.dart';
import '../providers/theme_provider.dart';
import 'login_screen.dart';
import 'purchase_history_screen.dart';
import 'notifications_screen.dart';
import 'buy_coffee_screen.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  Future<void> _showAccountDeletionDialog(BuildContext context,
      AuthService authService, UserService userService) async {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final TextEditingController reasonController = TextEditingController();

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Account',
          style: TextStyle(color: Theme.of(context).primaryColor),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently deleted.',
            ),
            const SizedBox(height: 16),
            const Text(
              'Please tell us why you\'re leaving:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                hintText: 'Reason for deletion (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              // Close the input dialog first
              Navigator.of(context).pop();

              // Create a GlobalKey for the loading dialog
              final GlobalKey<State> dialogKey = GlobalKey<State>();
              
              // Show loading dialog with a key for reliable dismissal
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext dialogContext) => AlertDialog(
                  key: dialogKey,
                  content: const Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Processing your request...'),
                    ],
                  ),
                ),
              );

              try {
                // Submit account deletion request
                final success = await userService.requestAccountDeletion(
                    reasonController.text.isNotEmpty
                        ? reasonController.text
                        : 'User requested account deletion');

                // Ensure the loading dialog is closed using the key
                if (dialogKey.currentContext != null) {
                  Navigator.of(dialogKey.currentContext!, rootNavigator: true).pop();
                }

                if (success) {
                  // Sign out after successful request
                  await authService.signOut();

                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                            'Account deletion request submitted. You will be contacted shortly.'),
                        duration: Duration(seconds: 4),
                      ),
                    );

                    Navigator.of(context).pushAndRemoveUntil(
                      MaterialPageRoute(
                          builder: (context) => const LoginScreen()),
                      (route) => false,
                    );
                  }
                } else if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: ${userService.errorMessage}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } catch (e) {
                // Make sure dialog is closed even if there's an exception
                if (dialogKey.currentContext != null) {
                  Navigator.of(dialogKey.currentContext!, rootNavigator: true).pop();
                }
                
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _showDataRequestDialog(BuildContext context,
      AuthService authService, UserService userService) async {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final TextEditingController purposeController = TextEditingController();

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Request Your Data',
          style: TextStyle(color: Theme.of(context).primaryColor),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'We will prepare a copy of all your personal data and send it to your registered email address within 30 days.',
            ),
            const SizedBox(height: 16),
            const Text(
              'Please tell us why you\'re requesting your data:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: purposeController,
              decoration: const InputDecoration(
                hintText: 'Purpose of data request (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              // Close the input dialog first
              Navigator.of(context).pop();

              // Create a GlobalKey for the loading dialog
              final GlobalKey<State> dialogKey = GlobalKey<State>();
              
              // Show loading dialog with a key for reliable dismissal
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext dialogContext) => AlertDialog(
                  key: dialogKey,
                  content: const Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Processing your request...'),
                    ],
                  ),
                ),
              );

              try {
                // Submit data access request
                final success = await userService.requestDataAccess(
                    purposeController.text.isNotEmpty
                        ? purposeController.text
                        : 'User requested data access');

                // Ensure the loading dialog is closed using the key
                if (dialogKey.currentContext != null) {
                  Navigator.of(dialogKey.currentContext!, rootNavigator: true).pop();
                }

                // Show success or error message
                if (success && context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                          'Data request submitted. You will receive an email soon.'),
                      duration: Duration(seconds: 4),
                    ),
                  );
                } else if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: ${userService.errorMessage}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } catch (e) {
                // Make sure dialog is closed even if there's an exception
                if (dialogKey.currentContext != null) {
                  Navigator.of(dialogKey.currentContext!, rootNavigator: true).pop();
                }
                
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Request Data'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);
    final userService = Provider.of<UserService>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isGuest = authService.isAnonymous;

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Profile'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 20),

            // User profile image
            if (isGuest)
              CircleAvatar(
                radius: 60,
                backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                child: Icon(
                  Icons.person_outline,
                  size: 60,
                  color: Theme.of(context).primaryColor,
                ),
              )
            else if (authService.photoURL.isNotEmpty)
              CircleAvatar(
                radius: 60,
                backgroundImage: NetworkImage(authService.photoURL),
                backgroundColor: Colors.grey.shade200,
              )
            else
              CircleAvatar(
                radius: 60,
                backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                child: Icon(
                  Icons.person,
                  size: 60,
                  color: Theme.of(context).primaryColor,
                ),
              ),

            const SizedBox(height: 20),

            // User name
            Text(
              isGuest ? 'Guest User' : authService.userDisplayName,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
              textAlign: TextAlign.center,
            ),

            // User email
            if (!isGuest && authService.userEmail.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                authService.userEmail,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade700,
                ),
                textAlign: TextAlign.center,
              ),
            ],

            if (isGuest) ...[
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                        builder: (context) => const LoginScreen()),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('Sign In for Full Access',
                    style: TextStyle(fontSize: 16)),
              ),
            ],

            const SizedBox(height: 40),

            // Account information section for logged-in users
            if (!isGuest) ...[
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Account Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    ListTile(
                      leading: Icon(Icons.history,
                          color: Theme.of(context).primaryColor),
                      title: const Text('Purchase History'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) =>
                                  const PurchaseHistoryScreen()),
                        );
                      },
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: Icon(Icons.notifications,
                          color: Theme.of(context).primaryColor),
                      title: const Text('Notifications'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) =>
                                  const NotificationsScreen()),
                        );
                      },
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: Icon(Icons.download_rounded,
                          color: Theme.of(context).primaryColor),
                      title: const Text('Request My Data'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () => _showDataRequestDialog(
                          context, authService, userService),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              // App settings section for logged-in users
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'App Settings',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    ListTile(
                      leading: Icon(Icons.language,
                          color: Theme.of(context).primaryColor),
                      title: const Text('Language'),
                      trailing: const Text('English'),
                      onTap: () {
                        // Open language selection
                      },
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: Icon(Icons.dark_mode,
                          color: Theme.of(context).primaryColor),
                      title: const Text('Dark Mode'),
                      subtitle: const Text('Coming soon'),
                      trailing: Switch(
                        value: false,
                        onChanged: (value) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: const Text('Dark mode coming soon!'),
                              backgroundColor: Colors.black87,
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        activeColor: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 30),

            // Support section (available for all users)
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Support',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
            const SizedBox(height: 16),

            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: ListTile(
                leading: Icon(Icons.coffee, color: Theme.of(context).primaryColor),
                title: const Text('Buy Developer Coffee'),
                subtitle: const Text('Support the app development'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const BuyCoffeeScreen()),
                  );
                },
              ),
            ),

            if (!isGuest) ...[
              const SizedBox(height: 30),

              // Account management section
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Account Management',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade700,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ListTile(
                  leading:
                      Icon(Icons.delete_forever, color: Colors.red.shade700),
                  title: const Text('Delete Account'),
                  subtitle:
                      const Text('Permanently delete your account and data'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () => _showAccountDeletionDialog(
                      context, authService, userService),
                ),
              ),

              const SizedBox(height: 40),

              // Sign out button
              ElevatedButton.icon(
                onPressed: () async {
                  await authService.signOut();
                  if (context.mounted) {
                    Navigator.of(context).pushAndRemoveUntil(
                      MaterialPageRoute(
                          builder: (context) => const LoginScreen()),
                      (route) => false,
                    );
                  }
                },
                icon: const Icon(Icons.logout),
                label: const Text('Sign Out'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade50,
                  foregroundColor: Colors.red.shade700,
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],

            const SizedBox(height: 40),

            FutureBuilder<String>(
              future: _getAppVersion(),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return Text(
                    'Version ${snapshot.data}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Future<String> _getAppVersion() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.version;
    } catch (e) {
      debugPrint('Error getting app version: $e');
      return '0.0.1';
    }
  }
}
