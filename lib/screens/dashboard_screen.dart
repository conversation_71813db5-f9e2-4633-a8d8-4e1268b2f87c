import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/cart.dart';
import '../models/product.dart';
import '../models/grocery_product.dart';
import '../models/category.dart';
import '../services/grocery_service.dart';
import '../widgets/cart_badge.dart';
import '../utils/category_utils.dart';
import 'cart_screen.dart';
import 'profile_screen.dart';
import 'dart:async';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  Timer? _searchDebounce;

  List<GroceryProduct> _displayedProducts = [];
  List<CategoryGroup> _displayedCategoryGroups = [];

  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedCategory = '';
  int _selectedIndex = 0;
  int _currentPage = 1;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    debugPrint('DashboardScreen initState called');
    _scrollController.addListener(_onScroll);
    _refreshData();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    _searchDebounce?.cancel();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading &&
          _hasMore &&
          _searchQuery.isEmpty &&
          _selectedCategory.isEmpty) {
        _loadMoreProducts();
      }
    }
  }

  Future<void> _refreshData() async {
    debugPrint('_refreshData called');
    setState(() {
      _isLoading = true;
      _displayedProducts = [];
      _displayedCategoryGroups = [];
      _currentPage = 1;
      _hasMore = true;
      _selectedCategory = '';
      _searchController.clear();
      _searchQuery = '';
    });

    try {
      debugPrint('Starting to fetch products and categories');
      // Load products and categories concurrently to avoid blocking
      final futures = await Future.wait([
        GroceryService.fetchProducts(page: _currentPage, pageSize: 20),
        GroceryService.fetchCategories(),
      ]);

      final productsResponse = futures[0] as ProductsResponse?;
      final categoriesResponse = futures[1] as CategoriesResponse?;

      final products = productsResponse?.products ?? [];
      final categories = categoriesResponse?.categories ?? [];

      debugPrint(
          'Received ${products.length} products and ${categories.length} categories');

      final nonEmptyCategories =
          categories.where((c) => c.productCount > 0).toList();
      final grouped = CategoryGroup.groupCategories(nonEmptyCategories);

      debugPrint('Grouped into ${grouped.length} category groups');

      if (mounted) {
        setState(() {
          _displayedProducts = products;
          _hasMore = productsResponse?.hasNext ?? false;
          _displayedCategoryGroups = grouped;
          _isLoading = false;
        });
        debugPrint('State updated with data');
      }
    } catch (e) {
      debugPrint('Error refreshing data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMoreProducts() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await GroceryService.fetchProducts(
          page: _currentPage + 1, pageSize: 20);
      if (response != null && response.products.isNotEmpty) {
        setState(() {
          _displayedProducts.addAll(response.products);
          _currentPage++;
          _hasMore = response.hasNext;
        });
      } else {
        setState(() {
          _hasMore = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading more products: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _onSearchSubmitted(String query) async {
    if (query.isEmpty) {
      _refreshData();
      return;
    }
    setState(() {
      _isLoading = true;
      _searchQuery = query;
      _selectedCategory = '';
      _displayedProducts = [];
    });
    final products =
        await GroceryService.searchProductsByCategory(category: query);
    setState(() {
      _displayedProducts = products;
      _isLoading = false;
      _hasMore = false;
    });
  }

  void _onCategorySelected(String categoryName) async {
    setState(() {
      _isLoading = true;
      _selectedCategory = categoryName;
      _searchQuery = '';
      _displayedProducts = [];
    });

    try {
      debugPrint('Searching for category: $categoryName');
      final products =
          await GroceryService.searchProductsByCategory(category: categoryName);
      debugPrint(
          'Found ${products.length} products for category: $categoryName');

      if (mounted) {
        setState(() {
          _displayedProducts = products;
          _isLoading = false;
          _hasMore = false;
        });
      }
    } catch (e) {
      debugPrint('Error searching category $categoryName: $e');
      if (mounted) {
        setState(() {
          _displayedProducts = [];
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }

  void _onBottomNavTap(int index) {
    setState(() {
      _selectedIndex = index;
    });
    switch (index) {
      case 0:
        _refreshData();
        break;
      case 1:
        Navigator.pushNamed(context, '/search');
        break;
      case 2:
        Navigator.push(context,
            MaterialPageRoute(builder: (context) => const CartScreen()));
        break;
      case 3:
        Navigator.push(context,
            MaterialPageRoute(builder: (context) => const ProfileScreen()));
        break;
    }
  }

  void _addToCart(GroceryProduct product) {
    // Don't add out-of-stock products
    if (product.price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('${product.name} is out of stock'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      ));
      return;
    }

    final cart = Provider.of<Cart>(context, listen: false);
    final cartProduct = Product(
        id: product.id,
        name: product.name,
        store: product.shopName,
        price: product.formattedPrice,
        imageUrl: product.primaryImage,
        category: product.primaryCategory,
        searchBarcode: '',
        brand: '',
        originalImageUrl: product.primaryImage,
        quantity: 1,
        url: '');
    cart.addProduct(cartProduct);
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text('${product.name} added to cart'),
      backgroundColor: Colors.green,
      duration: const Duration(seconds: 2),
    ));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      drawer: _buildDrawer(),
      appBar: _buildAppBar(theme),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            SliverToBoxAdapter(child: _buildSearchBar(theme)),
            SliverToBoxAdapter(child: _buildPromotionalBanners()),
            SliverToBoxAdapter(
                child: _buildSectionHeader("Categories", theme,
                    onSeeAllTapped: () {
              _searchFocusNode.requestFocus();
            })),
            SliverToBoxAdapter(child: _buildCategoryChips()),
            _buildContentSliver(),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavBar(theme),
    );
  }

  Widget _buildContentSliver() {
    if (_isLoading && _displayedProducts.isEmpty) {
      return const SliverFillRemaining(
          child: Center(child: CircularProgressIndicator()));
    }
    if (_displayedProducts.isEmpty) {
      return const SliverFillRemaining(
          child: Center(child: Text('No products found.')));
    }
    return _buildProductGrid();
  }

  AppBar _buildAppBar(ThemeData theme) {
    return AppBar(
      leading: Builder(
        builder: (context) => IconButton(
          icon:
              Icon(Icons.shopping_basket, color: theme.primaryColor, size: 30),
          onPressed: () => Scaffold.of(context).openDrawer(),
        ),
      ),
      title: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Location", style: theme.textTheme.bodySmall),
          Text("2972 Westheimer Rd.",
              style: theme.textTheme.bodyMedium
                  ?.copyWith(fontWeight: FontWeight.bold)),
        ],
      ),
      actions: [
        Consumer<Cart>(
          builder: (context, cart, child) => CartBadge(
            child: IconButton(
              icon: const Icon(Icons.shopping_cart_outlined),
              onPressed: () => Navigator.push(context,
                  MaterialPageRoute(builder: (context) => const CartScreen())),
            ),
          ),
        ),
        const Padding(
          padding: EdgeInsets.only(right: 16.0),
          child: CircleAvatar(
            radius: 20,
            backgroundImage: NetworkImage("https://i.pravatar.cc/150?img=12"),
          ),
        ),
      ],
      backgroundColor: theme.scaffoldBackgroundColor,
      elevation: 0,
    );
  }

  Drawer _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(color: Theme.of(context).primaryColor),
            child: const Text('Budget Basket',
                style: TextStyle(color: Colors.white, fontSize: 24)),
          ),
          ListTile(
              leading: const Icon(Icons.home),
              title: const Text('Home'),
              onTap: () {
                Navigator.pop(context);
                _onBottomNavTap(0);
              }),
          ListTile(
              leading: const Icon(Icons.search),
              title: const Text('Search'),
              onTap: () {
                Navigator.pop(context);
                _onBottomNavTap(1);
              }),
          ListTile(
              leading: const Icon(Icons.shopping_cart),
              title: const Text('Cart'),
              onTap: () {
                Navigator.pop(context);
                _onBottomNavTap(2);
              }),
          ListTile(
              leading: const Icon(Icons.person),
              title: const Text('Profile'),
              onTap: () {
                Navigator.pop(context);
                _onBottomNavTap(3);
              }),
        ],
      ),
    );
  }

  Widget _buildSearchBar(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        decoration: InputDecoration(
          hintText: "Search...",
          prefixIcon:
              Icon(Icons.search, color: theme.textTheme.bodySmall?.color),
          filled: true,
          fillColor: theme.cardColor,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
        ),
        onSubmitted: _onSearchSubmitted,
      ),
    );
  }

  Widget _buildPromotionalBanners() {
    return Container(
      height: 180,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildPromoCard(
              'Savon Stories', 'BUY 1 GET 1 FREE', Colors.yellow.shade200),
          const SizedBox(width: 16),
          _buildPromoCard('Fresh', 'BUY 1 GET 1 FREE', Colors.green.shade200),
        ],
      ),
    );
  }

  Widget _buildPromoCard(String title, String subtitle, Color bgColor) {
    return Container(
      width: 280,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text("Cold Process Organic",
              style: TextStyle(
                  fontSize: 14, color: Colors.black.withOpacity(0.7))),
          const SizedBox(height: 4),
          Text(title,
              style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black)),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green, size: 18),
              const SizedBox(width: 6),
              Text(subtitle,
                  style: const TextStyle(
                      fontWeight: FontWeight.w600, color: Colors.black)),
            ],
          ),
          const Spacer(),
          ElevatedButton(
            onPressed: () {},
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF53B175),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text("Shop Now"),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, ThemeData theme,
      {VoidCallback? onSeeAllTapped}) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title,
              style: theme.textTheme.titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold)),
          if (onSeeAllTapped != null)
            InkWell(
              onTap: onSeeAllTapped,
              child: Text("See All >",
                  style: theme.textTheme.bodyMedium
                      ?.copyWith(color: theme.primaryColor)),
            )
        ],
      ),
    );
  }

  Widget _buildCategoryChips() {
    if (_isLoading && _displayedCategoryGroups.isEmpty) {
      return const Center(
          child: Padding(
              padding: EdgeInsets.all(8.0),
              child: Text("Loading categories...")));
    }

    // Get a unique list of top-level categories with products
    final allCategories =
        _displayedCategoryGroups.expand((group) => group.categories).toList();
    final topLevelCategories = allCategories
        .where((c) => c.productCount > 0) // Only show categories with products
        .map((c) => c.topLevelCategory)
        .toSet()
        .toList();

    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: topLevelCategories.length + 1, // +1 for "All"
        itemBuilder: (context, index) {
          if (index == 0) {
            // "All" chip
            final bool isAllSelected = _selectedCategory.isEmpty;
            return Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: ChoiceChip(
                label: const Text("All"),
                selected: isAllSelected,
                onSelected: (selected) {
                  if (selected) {
                    _refreshData();
                  }
                },
                backgroundColor:
                    isAllSelected ? Colors.black : Theme.of(context).cardColor,
                selectedColor: Colors.black,
                labelStyle: TextStyle(
                    color: isAllSelected ? Colors.white : Colors.black),
              ),
            );
          }

          final categoryName = topLevelCategories[index - 1];
          final bool isSelected = categoryName == _selectedCategory;

          return Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: ChoiceChip(
              label: Text(categoryName),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  _onCategorySelected(categoryName);
                }
              },
              backgroundColor:
                  isSelected ? Colors.black : Theme.of(context).cardColor,
              selectedColor: Colors.black,
              labelStyle:
                  TextStyle(color: isSelected ? Colors.white : Colors.black),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProductGrid() {
    return SliverPadding(
      padding: const EdgeInsets.all(16),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          childAspectRatio: 0.7,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (index >= _displayedProducts.length) return null;
            return _buildProductCard(_displayedProducts[index], index);
          },
          childCount: _displayedProducts.length,
        ),
      ),
    );
  }

  Widget _buildBottomNavBar(ThemeData theme) {
    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      onTap: _onBottomNavTap,
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home_outlined), label: "Home"),
        BottomNavigationBarItem(icon: Icon(Icons.search), label: "Search"),
        BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart_outlined), label: "Cart"),
        BottomNavigationBarItem(
            icon: Icon(Icons.person_outline), label: "Profile"),
      ],
    );
  }

  Widget _buildProductCard(GroceryProduct product, int index) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          // TODO: Navigate to product details screen
        },
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 5,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Stack(
                  alignment: Alignment.topLeft,
                  children: [
                    Positioned.fill(
                      child: ClipRRect(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(12)),
                        child: product.primaryImage.isNotEmpty
                            ? Image.network(
                                product.primaryImage,
                                fit: BoxFit.cover,
                                headers: {
                                  'User-Agent':
                                      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                                  'Accept':
                                      'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                                  'Accept-Language': 'en-US,en;q=0.9',
                                  'Accept-Encoding': 'gzip, deflate, br, zstd',
                                  'Referer': 'https://www.shoprite.co.za/',
                                  'Sec-Fetch-Dest': 'image',
                                  'Sec-Fetch-Mode': 'no-cors',
                                  'Sec-Fetch-Site': 'same-origin',
                                  'Sec-Ch-Ua':
                                      '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                                  'Sec-Ch-Ua-Mobile': '?0',
                                  'Sec-Ch-Ua-Platform': '"Windows"',
                                  'Priority': 'i',
                                },
                                loadingBuilder:
                                    (context, child, loadingProgress) {
                                  if (loadingProgress == null) return child;
                                  return Container(
                                    color: Colors.grey[200],
                                    child: Center(
                                      child: CircularProgressIndicator(
                                        value: loadingProgress
                                                    .expectedTotalBytes !=
                                                null
                                            ? loadingProgress
                                                    .cumulativeBytesLoaded /
                                                loadingProgress
                                                    .expectedTotalBytes!
                                            : null,
                                      ),
                                    ),
                                  );
                                },
                                errorBuilder: (context, error, stackTrace) {
                                  debugPrint(
                                      'Image loading error for ${product.primaryImage}: $error');
                                  // Create a colored placeholder with product name
                                  final colors = [
                                    Colors.blue.shade100,
                                    Colors.green.shade100,
                                    Colors.orange.shade100,
                                    Colors.purple.shade100,
                                    Colors.teal.shade100,
                                    Colors.pink.shade100,
                                  ];
                                  final colorIndex =
                                      product.name.hashCode % colors.length;
                                  final backgroundColor =
                                      colors[colorIndex.abs()];

                                  return Container(
                                    color: backgroundColor,
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.shopping_basket_outlined,
                                          color: Colors.grey[600],
                                          size: 32,
                                        ),
                                        const SizedBox(height: 8),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 4),
                                          child: Text(
                                            product.name
                                                .split(' ')
                                                .take(3)
                                                .join(' '),
                                            style: TextStyle(
                                              color: Colors.grey[700],
                                              fontSize: 9,
                                              fontWeight: FontWeight.w500,
                                            ),
                                            textAlign: TextAlign.center,
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              )
                            : Container(
                                color: Colors.grey[200],
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.shopping_basket_outlined,
                                        color: Colors.grey[400], size: 40),
                                    const SizedBox(height: 4),
                                    Text(
                                      'No Image',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 10,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                      ),
                    ),
                    if (product.hasDiscount)
                      Container(
                        margin: const EdgeInsets.all(8),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.redAccent,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '${(product.discount * 100).toInt()}% OFF',
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            // Product Details
            Expanded(
              flex: 4,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      product.name,
                      style: theme.textTheme.bodyLarge
                          ?.copyWith(fontWeight: FontWeight.w600),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (product.price > 0) ...[
                              if (product.hasDiscount)
                                Text(
                                  product.formattedPrice,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                      decoration: TextDecoration.lineThrough),
                                ),
                              Text(
                                product.discountedPrice,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: theme.primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ] else
                              Text(
                                'Out of Stock',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: Colors.red.shade700,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          ],
                        ),
                        product.price > 0
                            ? InkWell(
                                onTap: () => _addToCart(product),
                                borderRadius: BorderRadius.circular(50),
                                child: Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: theme.primaryColor,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(Icons.add,
                                      color: Colors.white, size: 20),
                                ),
                              )
                            : Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.red.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Out of Stock',
                                  style: TextStyle(
                                    color: Colors.red.shade700,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
