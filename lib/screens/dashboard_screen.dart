import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/cart.dart';
import '../models/product.dart';
import '../models/grocery_product.dart';
import '../models/category.dart';
import '../services/grocery_service.dart';
import '../widgets/cart_badge.dart';
import '../widgets/network_image_with_fallback.dart';
import '../utils/category_utils.dart';
import 'cart_screen.dart';
import 'profile_screen.dart';
import 'product_details_screen.dart';
import 'dart:async';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final ScrollController _scrollController = ScrollController();

  Timer? _searchDebounce;

  List<GroceryProduct> _displayedProducts = [];
  List<CategoryGroup> _displayedCategoryGroups = [];

  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedCategory = '';
  int _selectedIndex = 0;
  int _currentPage = 1;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    debugPrint('DashboardScreen initState called');
    _scrollController.addListener(_onScroll);
    _refreshData();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchDebounce?.cancel();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading &&
          _hasMore &&
          _searchQuery.isEmpty &&
          _selectedCategory.isEmpty) {
        _loadMoreProducts();
      }
    }
  }

  Future<void> _refreshData() async {
    debugPrint('_refreshData called');
    setState(() {
      _isLoading = true;
      _displayedProducts = [];
      _displayedCategoryGroups = [];
      _currentPage = 1;
      _hasMore = true;
      _selectedCategory = '';
      _searchQuery = '';
    });

    try {
      debugPrint('Starting to fetch products and categories');
      // Load products and categories concurrently to avoid blocking
      final futures = await Future.wait([
        GroceryService.fetchProducts(page: _currentPage, pageSize: 20),
        GroceryService.fetchCategories(),
      ]);

      final productsResponse = futures[0] as ProductsResponse?;
      final categoriesResponse = futures[1] as CategoriesResponse?;

      final products = productsResponse?.products ?? [];
      final categories = categoriesResponse?.categories ?? [];

      // Shuffle the products
      products.shuffle();

      debugPrint(
          'Received ${products.length} products and ${categories.length} categories');

      final nonEmptyCategories =
          categories.where((c) => c.productCount > 0).toList();
      final grouped = CategoryGroup.groupCategories(nonEmptyCategories);

      debugPrint('Grouped into ${grouped.length} category groups');

      if (mounted) {
        setState(() {
          _displayedProducts = products;
          _hasMore = productsResponse?.hasNext ?? false;
          _displayedCategoryGroups = grouped;
          _isLoading = false;
        });
        debugPrint('State updated with data');
      }
    } catch (e) {
      debugPrint('Error refreshing data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMoreProducts() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await GroceryService.fetchProducts(
          page: _currentPage + 1, pageSize: 20);
      if (response != null && response.products.isNotEmpty) {
        setState(() {
          _displayedProducts.addAll(response.products);
          _currentPage++;
          _hasMore = response.hasNext;
        });
      } else {
        setState(() {
          _hasMore = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading more products: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _onCategorySelected(String categoryName) async {
    setState(() {
      _isLoading = true;
      _selectedCategory = categoryName;
      _searchQuery = '';
      _displayedProducts = [];
    });

    try {
      debugPrint('Searching for category: $categoryName');
      final products =
          await GroceryService.searchProductsByCategory(category: categoryName);
      debugPrint(
          'Found ${products.length} products for category: $categoryName');

      // Shuffle the products
      products.shuffle();

      if (mounted) {
        setState(() {
          _displayedProducts = products;
          _isLoading = false;
          _hasMore = false;
        });
      }
    } catch (e) {
      debugPrint('Error searching category $categoryName: $e');
      if (mounted) {
        setState(() {
          _displayedProducts = [];
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }

  void _onBottomNavTap(int index) {
    setState(() {
      _selectedIndex = index;
    });
    switch (index) {
      case 0:
        _refreshData();
        break;
      case 1:
        Navigator.pushNamed(context, '/search');
        break;
      case 2:
        Navigator.push(context,
            MaterialPageRoute(builder: (context) => const CartScreen()));
        break;
      case 3:
        Navigator.push(context,
            MaterialPageRoute(builder: (context) => const ProfileScreen()));
        break;
    }
  }

  void _addToCart(GroceryProduct product) {
    // Don't add out-of-stock products
    if (product.price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('${product.name} is out of stock'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      ));
      return;
    }

    final cart = Provider.of<Cart>(context, listen: false);
    final cartProduct = Product(
        id: product.id,
        name: product.name,
        store: product.shopName,
        price: product.formattedPrice,
        imageUrl: product.primaryImage,
        category: product.primaryCategory,
        searchBarcode: '',
        brand: '',
        originalImageUrl: product.primaryImage,
        quantity: 1,
        url: '');
    cart.addProduct(cartProduct);
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text('${product.name} added to cart'),
      backgroundColor: Colors.green,
      duration: const Duration(seconds: 2),
    ));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      drawer: _buildDrawer(),
      appBar: _buildAppBar(theme),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            SliverToBoxAdapter(child: _buildPromotionalBanners()),
            SliverToBoxAdapter(
                child: _buildSectionHeader("Categories", theme,
                    onSeeAllTapped: () {
              Navigator.pushNamed(context, '/search');
            })),
            SliverToBoxAdapter(child: _buildCategoryChips()),
            _buildContentSliver(),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavBar(theme),
    );
  }

  Widget _buildContentSliver() {
    if (_isLoading && _displayedProducts.isEmpty) {
      return const SliverFillRemaining(
          child: Center(child: CircularProgressIndicator()));
    }
    if (_displayedProducts.isEmpty) {
      return const SliverFillRemaining(
          child: Center(child: Text('No products found.')));
    }
    return _buildProductGrid();
  }

  AppBar _buildAppBar(ThemeData theme) {
    return AppBar(
      leading: Builder(
        builder: (context) => IconButton(
          icon: Icon(Icons.shopping_basket, color: Colors.white, size: 30),
          onPressed: () => Scaffold.of(context).openDrawer(),
        ),
      ),
      title: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Location",
              style:
                  theme.textTheme.bodySmall?.copyWith(color: Colors.white70)),
          Text("2972 Westheimer Rd.",
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              )),
        ],
      ),
      actions: [
        Consumer<Cart>(
          builder: (context, cart, child) => CartBadge(
            child: IconButton(
              icon:
                  const Icon(Icons.shopping_cart_outlined, color: Colors.white),
              onPressed: () => Navigator.push(context,
                  MaterialPageRoute(builder: (context) => const CartScreen())),
            ),
          ),
        ),
        const SizedBox(width: 16), // Add some spacing on the right
      ],
      backgroundColor: const Color(0xFF53B175), // Grocery green color
      elevation: 2,
      shadowColor: Colors.black.withValues(alpha: 0.1),
    );
  }

  Drawer _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(color: Theme.of(context).primaryColor),
            child: const Text('Budget Basket',
                style: TextStyle(color: Colors.white, fontSize: 24)),
          ),
          ListTile(
              leading: const Icon(Icons.home),
              title: const Text('Home'),
              onTap: () {
                Navigator.pop(context);
                _onBottomNavTap(0);
              }),
          ListTile(
              leading: const Icon(Icons.search),
              title: const Text('Search'),
              onTap: () {
                Navigator.pop(context);
                _onBottomNavTap(1);
              }),
          ListTile(
              leading: const Icon(Icons.shopping_cart),
              title: const Text('Cart'),
              onTap: () {
                Navigator.pop(context);
                _onBottomNavTap(2);
              }),
          ListTile(
              leading: const Icon(Icons.person),
              title: const Text('Profile'),
              onTap: () {
                Navigator.pop(context);
                _onBottomNavTap(3);
              }),
        ],
      ),
    );
  }

  Widget _buildPromotionalBanners() {
    return Container(
      height: 210,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildPromoCard(
              'Savon Stories', 'BUY 1 GET 1 FREE', Colors.yellow.shade200),
          const SizedBox(width: 16),
          _buildPromoCard('Fresh', 'BUY 1 GET 1 FREE', Colors.green.shade200),
        ],
      ),
    );
  }

  Widget _buildPromoCard(String title, String subtitle, Color bgColor) {
    return Container(
      width: 280,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text("Cold Process Organic",
              style: TextStyle(
                  fontSize: 14, color: Colors.black.withValues(alpha: 0.7))),
          const SizedBox(height: 4),
          Text(title,
              style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black)),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green, size: 18),
              const SizedBox(width: 6),
              Expanded(
                child: Text(subtitle,
                    style: const TextStyle(
                        fontWeight: FontWeight.w600, color: Colors.black),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis),
              ),
            ],
          ),
          const Spacer(),
          ElevatedButton(
            onPressed: () {},
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF53B175),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text("Shop Now"),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, ThemeData theme,
      {VoidCallback? onSeeAllTapped}) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title,
              style: theme.textTheme.titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold)),
          if (onSeeAllTapped != null)
            InkWell(
              onTap: onSeeAllTapped,
              child: Text("See All >",
                  style: theme.textTheme.bodyMedium
                      ?.copyWith(color: theme.primaryColor)),
            )
        ],
      ),
    );
  }

  Widget _buildCategoryChips() {
    if (_isLoading && _displayedCategoryGroups.isEmpty) {
      return const Center(
          child: Padding(
              padding: EdgeInsets.all(8.0),
              child: Text("Loading categories...")));
    }

    // Get a unique list of top-level categories with products
    final allCategories =
        _displayedCategoryGroups.expand((group) => group.categories).toList();
    final topLevelCategories = allCategories
        .where((c) => c.productCount > 0) // Only show categories with products
        .map((c) => c.topLevelCategory)
        .toSet()
        .toList();

    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: topLevelCategories.length + 1, // +1 for "All"
        itemBuilder: (context, index) {
          if (index == 0) {
            // "All" chip
            final bool isAllSelected = _selectedCategory.isEmpty;
            return Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: ChoiceChip(
                label: const Text("Featured"),
                selected: isAllSelected,
                onSelected: (selected) {
                  if (selected) {
                    _refreshData();
                  }
                },
                backgroundColor:
                    isAllSelected ? Colors.black : Theme.of(context).cardColor,
                selectedColor: Colors.black,
                labelStyle: TextStyle(
                    color: isAllSelected ? Colors.white : Colors.black),
              ),
            );
          }

          final categoryName = topLevelCategories[index - 1];
          final bool isSelected = categoryName == _selectedCategory;

          return Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: ChoiceChip(
              label: Text(categoryName),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  _onCategorySelected(categoryName);
                }
              },
              backgroundColor:
                  isSelected ? Colors.black : Theme.of(context).cardColor,
              selectedColor: Colors.black,
              labelStyle:
                  TextStyle(color: isSelected ? Colors.white : Colors.black),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProductGrid() {
    return SliverPadding(
      padding: const EdgeInsets.all(16),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          childAspectRatio: 0.7,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (index >= _displayedProducts.length) return null;
            return _buildProductCard(_displayedProducts[index], index);
          },
          childCount: _displayedProducts.length,
        ),
      ),
    );
  }

  Widget _buildBottomNavBar(ThemeData theme) {
    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      onTap: _onBottomNavTap,
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home_outlined), label: "Home"),
        BottomNavigationBarItem(icon: Icon(Icons.search), label: "Search"),
        BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart_outlined), label: "Cart"),
        BottomNavigationBarItem(
            icon: Icon(Icons.person_outline), label: "Profile"),
      ],
    );
  }

  Widget _buildProductCard(GroceryProduct product, int index) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProductDetailsScreen(product: product),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 5,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Stack(
                  children: [
                    Positioned.fill(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: NetworkImageWithFallback(
                          imageUrl: product.primaryImage,
                          fit: BoxFit.cover,
                          fallbackText: product.name,
                        ),
                      ),
                    ),
                    if (product.hasDiscount)
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'SAVE',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 4,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      product.name,
                      style: theme.textTheme.bodyMedium
                          ?.copyWith(fontWeight: FontWeight.w600),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (product.price > 0) ...[
                              if (product.hasDiscount)
                                Text(
                                  product.formattedPrice,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    decoration: TextDecoration.lineThrough,
                                    color: Colors.grey,
                                  ),
                                ),
                              Text(
                                product.discountedPrice,
                                style: theme.textTheme.titleSmall?.copyWith(
                                  color: theme.primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ]
                          ],
                        ),
                        product.price > 0
                            ? InkWell(
                                onTap: () => _addToCart(product),
                                child: Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: theme.primaryColor,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(Icons.add,
                                      color: Colors.white, size: 16),
                                ),
                              )
                            : Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.red.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  'Out of Stock',
                                  style: TextStyle(
                                    color: Colors.red.shade700,
                                    fontSize: 8,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
