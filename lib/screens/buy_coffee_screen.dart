import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import 'payment_screen.dart';

class BuyCoffeeScreen extends StatefulWidget {
  const BuyCoffeeScreen({Key? key}) : super(key: key);

  @override
  State<BuyCoffeeScreen> createState() => _BuyCoffeeScreenState();
}

class _BuyCoffeeScreenState extends State<BuyCoffeeScreen> {
  final TextEditingController _amountController = TextEditingController(text: '30');
  final double _minAmount = 20.0;
  final double _defaultAmount = 30.0;
  String? _errorText;
  
  @override
  void initState() {
    super.initState();
    _validateAmount(_defaultAmount.toString());
  }
  
  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }
  
  void _validateAmount(String value) {
    if (value.isEmpty) {
      setState(() {
        _errorText = 'Please enter an amount';
      });
      return;
    }
    
    try {
      final amount = double.parse(value);
      if (amount < _minAmount) {
        setState(() {
          _errorText = 'Minimum amount is R$_minAmount';
        });
      } else {
        setState(() {
          _errorText = null;
        });
      }
    } catch (e) {
      setState(() {
        _errorText = 'Please enter a valid amount';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Buy Me a Coffee'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Coffee icon
              Icon(
                Icons.coffee,
                size: 80,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(height: 24),
              
              // Title
              Text(
                'Support Budget Basket',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              
              // Description
              const Text(
                'Your support helps us improve Budget Basket and add new features. Choose an amount to contribute:',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              
              // Predefined amounts
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildAmountButton(20),
                  _buildAmountButton(30),
                  _buildAmountButton(50),
                  _buildAmountButton(100),
                ],
              ),
              const SizedBox(height: 32),
              
              // Custom amount input
              TextField(
                controller: _amountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Custom Amount (R)',
                  prefixText: 'R',
                  errorText: _errorText,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Theme.of(context).primaryColor, width: 2),
                  ),
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                onChanged: _validateAmount,
              ),
              const SizedBox(height: 8),
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Minimum amount: R$_minAmount',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
              const SizedBox(height: 40),
              
              // Proceed button
              ElevatedButton(
                onPressed: _errorText == null
                    ? () {
                        final amount = _amountController.text;
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => PaymentScreen(
                              amount: amount,
                              description: 'Thank you for your support of R$amount! Your contribution helps us improve Budget Basket and add new features.',
                            ),
                          ),
                        );
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  disabledBackgroundColor: Colors.grey[300],
                ),
                child: const Text('Proceed to Payment', style: TextStyle(fontSize: 16)),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildAmountButton(int amount) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isSelected = _amountController.text == amount.toString();
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _amountController.text = amount.toString();
          _validateAmount(amount.toString());
        });
      },
      child: Container(
        width: 70,
        height: 70,
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor : Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'R$amount',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.white : Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
