import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../services/notification_service.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize notification settings
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationService>().initializeSettings();
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final notificationService = Provider.of<NotificationService>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: notificationService.isLoading
          ? const Center(child: CircularProgressIndicator())
          : notificationService.error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        notificationService.error!,
                        style: TextStyle(color: Colors.red[700]),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => notificationService.initializeSettings(),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notification Preferences',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildPreferenceCard(
                        title: 'General Notifications',
                        children: [
                          SwitchListTile(
                            title: const Text('Push Notifications'),
                            subtitle: const Text('Receive notifications on your device'),
                            value: notificationService.settings?.preferences.pushNotifications ?? false,
                            onChanged: (value) async {
                              final success = await notificationService.updateSettings(
                                pushNotifications: value,
                              );
                              if (!success && mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Failed to update notification settings'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                          ),
                          SwitchListTile(
                            title: const Text('Email Notifications'),
                            subtitle: const Text('Receive notifications via email'),
                            value: notificationService.settings?.preferences.emailNotifications ?? false,
                            onChanged: (value) async {
                              final success = await notificationService.updateSettings(
                                emailNotifications: value,
                              );
                              if (!success && mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Failed to update notification settings'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                          ),
                          SwitchListTile(
                            title: const Text('Special Offers'),
                            subtitle: const Text('Get notified about deals and promotions'),
                            value: notificationService.settings?.preferences.specialOffers ?? false,
                            onChanged: (value) async {
                              final success = await notificationService.updateSettings(
                                specialOffers: value,
                              );
                              if (!success && mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Failed to update notification settings'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildPreferenceCard(
                        title: 'Shopping Alerts',
                        children: [
                          SwitchListTile(
                            title: const Text('Price Alerts'),
                            subtitle: const Text('Get notified when prices change'),
                            value: notificationService.settings?.categories.priceAlerts ?? false,
                            onChanged: (value) async {
                              final success = await notificationService.updateSettings(
                                priceAlerts: value,
                              );
                              if (!success && mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Failed to update notification settings'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                          ),
                          SwitchListTile(
                            title: const Text('New Products'),
                            subtitle: const Text('Get notified about new items'),
                            value: notificationService.settings?.categories.newProducts ?? false,
                            onChanged: (value) async {
                              final success = await notificationService.updateSettings(
                                newProducts: value,
                              );
                              if (!success && mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Failed to update notification settings'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                          ),
                          SwitchListTile(
                            title: const Text('Stock Alerts'),
                            subtitle: const Text('Get notified about stock changes'),
                            value: notificationService.settings?.categories.stockAlerts ?? false,
                            onChanged: (value) async {
                              final success = await notificationService.updateSettings(
                                stockAlerts: value,
                              );
                              if (!success && mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Failed to update notification settings'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
    );
  }

  Widget _buildPreferenceCard({
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const Divider(height: 1),
          ...children,
        ],
      ),
    );
  }
}
