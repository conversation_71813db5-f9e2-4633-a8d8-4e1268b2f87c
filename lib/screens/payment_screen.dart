import 'package:flutter/material.dart';
import '../services/payment_service.dart';
import '../services/deep_link_service.dart';

class PaymentScreen extends StatefulWidget {
  final String amount;
  final String description;

  const PaymentScreen({
    Key? key,
    required this.amount,
    required this.description,
  }) : super(key: key);

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  late TransactionStatus _status;
  final _paymentService = PaymentService();

  @override
  void initState() {
    super.initState();
    _status = TransactionStatus.processing;
    _setupPaymentCallback();
  }

  void _setupPaymentCallback() {
    DeepLinkService.setPaymentCallback((String path) {
      switch (path) {
        case '/success':
          setState(() => _status = TransactionStatus.success);
          break;
        case '/error':
          setState(() => _status = TransactionStatus.failed);
          break;
        case '/cancel':
          setState(() => _status = TransactionStatus.cancelled);
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // Status indicator
          Container(
            padding: const EdgeInsets.all(16),
            color: _getStatusColor(_status),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getStatusIcon(_status),
                  color: Colors.white,
                ),
                const SizedBox(width: 8),
                Text(
                  _getStatusText(_status),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // Payment details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Amount: R${widget.amount}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  widget.description,
                  style: TextStyle(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          
          // Payment widget
          if (_status == TransactionStatus.processing)
            Expanded(
              child: _paymentService.processPayment(
                amount: widget.amount,
                bankRef: 'BudgetBasket',
                onStatusChange: (status) {
                  setState(() => _status = status);
                },
              ),
            )
          else
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _getStatusIcon(_status),
                      size: 64,
                      color: _getStatusColor(_status),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _getStatusMessage(_status),
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 18),
                    ),
                    const SizedBox(height: 24),
                    if (_status != TransactionStatus.processing)
                      ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Done'),
                      ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Color _getStatusColor(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.processing:
        return Colors.blue;
      case TransactionStatus.success:
        return Colors.green;
      case TransactionStatus.failed:
        return Colors.red;
      case TransactionStatus.cancelled:
        return Colors.orange;
    }
  }

  IconData _getStatusIcon(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.processing:
        return Icons.pending;
      case TransactionStatus.success:
        return Icons.check_circle;
      case TransactionStatus.failed:
        return Icons.error;
      case TransactionStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getStatusText(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.processing:
        return 'Processing Payment';
      case TransactionStatus.success:
        return 'Payment Successful';
      case TransactionStatus.failed:
        return 'Payment Failed';
      case TransactionStatus.cancelled:
        return 'Payment Cancelled';
    }
  }

  String _getStatusMessage(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.success:
        return 'Thank you for your support!\nYour payment was successful.';
      case TransactionStatus.failed:
        return 'Sorry, there was a problem processing your payment.\nPlease try again.';
      case TransactionStatus.cancelled:
        return 'The payment was cancelled.\nFeel free to try again later.';
      case TransactionStatus.processing:
        return '';
    }
  }
}
