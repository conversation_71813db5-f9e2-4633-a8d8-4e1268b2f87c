import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/cart.dart';
import '../widgets/cached_image_widget.dart';
import '../providers/theme_provider.dart';
import '../services/auth_service.dart';
import '../services/order_service.dart';
import 'scanner_screen.dart';
import 'manual_item_screen.dart';
import 'summary_screen.dart';

class CartScreen extends StatelessWidget {
  const CartScreen({Key? key}) : super(key: key);

  // Function to open product URL in browser
  Future<void> _openProductUrl(BuildContext context, String url) async {
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No product URL available'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open product URL'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final cart = Provider.of<Cart>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Shopping Cart'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white, // Always white text for top bar
        actions: [
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () {
              showDialog(
                context: context,
                builder: (ctx) => AlertDialog(
                  title: const Text('Clear Cart'),
                  content: const Text(
                      'Are you sure you want to clear your shopping cart?'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(ctx).pop(),
                      child: const Text('CANCEL'),
                    ),
                    TextButton(
                      onPressed: () {
                        cart.clear();
                        Navigator.of(ctx).pop();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.red,
                      ),
                      child: const Text('CLEAR'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: cart.items.isEmpty
          ? _buildEmptyCart(context)
          : _buildCartItems(context, cart),
      bottomNavigationBar:
          cart.items.isEmpty ? null : _buildBottomBar(context, cart),
    );
  }

  Widget _buildEmptyCart(BuildContext context) {
    final cart = Provider.of<Cart>(context, listen: false);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDefaultStore = cart.selectedStore == 'Default';

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 100,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 20),
          const Text(
            'Your trolley is empty',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Add some items to your cart',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: () {
              // First pop the current screen
              Navigator.of(context).pop();
              // Then push the appropriate screen
              Future.delayed(Duration(milliseconds: 100), () {
                if (context.mounted) {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => isDefaultStore
                          ? const ManualItemScreen()
                          : const ScannerScreen(),
                    ),
                  );
                }
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            icon: Icon(
                isDefaultStore ? Icons.add_shopping_cart : Icons.camera_alt),
            label: Text(
              isDefaultStore ? 'Add Products' : 'Scan Products',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCartItems(BuildContext context, Cart cart) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Column(
      children: [
        // Budget info card
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Budget:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'R${cart.budget.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Total:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'R${cart.totalPrice.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 16,
                          color: cart.isOverBudget
                              ? Colors.red
                              : Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: cart.budget > 0
                        ? (cart.totalPrice / cart.budget).clamp(0.0, 1.0)
                        : 0,
                    backgroundColor: Colors.grey.shade200,
                    color: cart.isOverBudget
                        ? Colors.red
                        : Theme.of(context).primaryColor,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    cart.isOverBudget
                        ? 'Over Budget: R${(cart.totalPrice - cart.budget).toStringAsFixed(2)}'
                        : 'Remaining: R${cart.remainingBudget.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 16,
                      color: cart.isOverBudget
                          ? Colors.red
                          : Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Cart items
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            itemCount: cart.items.length,
            itemBuilder: (context, index) {
              final product = cart.items[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product image with tap to open URL
                      GestureDetector(
                        onTap: () => _openProductUrl(context, product.url),
                        child: Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: CachedImageWidget(
                                imageUrl: product.sanitizedImageUrl,
                                width: 60,
                                height: 60,
                                fit: BoxFit.cover,
                              ),
                            ),
                            // Overlay hint to indicate tappable
                            Positioned(
                              right: 0,
                              bottom: 0,
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.6),
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(8),
                                    bottomRight: Radius.circular(8),
                                  ),
                                ),
                                child: const Icon(
                                  Icons.open_in_new,
                                  color: Colors.white,
                                  size: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Product details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    product.name,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                // Remove item button
                                IconButton(
                                  icon: const Icon(Icons.delete_outline,
                                      color: Colors.red),
                                  onPressed: () {
                                    // Show confirmation dialog before removing
                                    showDialog(
                                      context: context,
                                      builder: (ctx) => AlertDialog(
                                        title: const Text('Remove Item'),
                                        content: Text(
                                            'Are you sure you want to remove ${product.name} from your trollery?'),
                                        actions: [
                                          TextButton(
                                            onPressed: () =>
                                                Navigator.of(ctx).pop(),
                                            child: const Text('CANCEL'),
                                          ),
                                          TextButton(
                                            onPressed: () {
                                              cart.removeProduct(product.id);
                                              Navigator.of(ctx).pop();
                                            },
                                            style: TextButton.styleFrom(
                                              foregroundColor: Colors.red,
                                            ),
                                            child: const Text('REMOVE'),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                  tooltip: 'Remove item',
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Flexible(
                                  child: Text(
                                    '${product.formattedPrice} × ${product.quantity}',
                                    style: const TextStyle(fontSize: 16),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Flexible(
                                  child: Text(
                                    'R${product.totalPrice.toStringAsFixed(2)}',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // Quantity controls
                      Row(
                        children: [
                          IconButton(
                            onPressed: () {
                              if (product.quantity > 1) {
                                cart.updateQuantity(
                                    product.id, product.quantity - 1);
                              } else {
                                cart.removeProduct(product.id);
                              }
                            },
                            icon: const Icon(Icons.remove_circle_outline),
                            color: Colors.red,
                          ),
                          Text(
                            '${product.quantity}',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              final bool success = cart.updateQuantity(
                                  product.id, product.quantity + 1);

                              if (!success) {
                                // Show error message
                                String errorMessage = '';
                                if (cart.hasReachedMaxItems) {
                                  errorMessage =
                                      'Cannot add more items. Maximum cart limit of ${Cart.maxItemCount} items reached.';
                                } else if (cart.hasReachedMaxAmount) {
                                  errorMessage =
                                      'Cannot add more items. Maximum cart amount of R${Cart.maxTotalAmount.toStringAsFixed(2)} reached.';
                                } else {
                                  errorMessage =
                                      'Cannot add more items. Cart limits reached.';
                                }

                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(errorMessage),
                                    duration: const Duration(seconds: 3),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                            icon: const Icon(Icons.add_circle_outline),
                            color: Colors.green,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBar(BuildContext context, Cart cart) {
    // Calculate total quantity
    final int totalQuantity =
        cart.items.fold(0, (sum, item) => sum + item.quantity);
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Cart summary
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Total',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'R${cart.totalPrice.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: cart.isOverBudget
                          ? Colors.red
                          : Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const Text(
                    'Items',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '$totalQuantity units',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // Continue shopping button
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    final cart = Provider.of<Cart>(context, listen: false);
                    if (cart.selectedStore == 'Default') {
                      // First pop the current screen
                      Navigator.of(context).pop();
                      // Then push the manual item screen
                      Future.delayed(Duration(milliseconds: 100), () {
                        if (context.mounted) {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const ManualItemScreen(),
                            ),
                          );
                        }
                      });
                    } else {
                      // First pop the current screen
                      Navigator.of(context).pop();
                      // Then push the scanner screen
                      Future.delayed(Duration(milliseconds: 100), () {
                        if (context.mounted) {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const ScannerScreen(),
                            ),
                          );
                        }
                      });
                    }
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Theme.of(context).primaryColor),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: const Text('Continue Shopping'),
                ),
              ),
              const SizedBox(width: 16),

              // Finish shopping button
              Expanded(
                child: ElevatedButton(
                  onPressed: () async {
                    final authService =
                        Provider.of<AuthService>(context, listen: false);
                    final orderService = OrderService();

                    // Show loading indicator
                    showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (BuildContext context) {
                        return const AlertDialog(
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('Saving your order...'),
                            ],
                          ),
                        );
                      },
                    );

                    // Save order to API
                    final success =
                        await orderService.saveOrder(cart, authService);

                    // Close loading dialog
                    if (context.mounted) {
                      Navigator.of(context).pop();
                    }

                    // Show success message only for logged-in users, not for anonymous users
                    if (success && context.mounted) {
                      // Only show success message if user is not anonymous
                      if (authService.user != null &&
                          !authService.user!.isAnonymous) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Order saved successfully!'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    }

                    // Show error message
                    if (!success && context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                              'Failed to save order. Please try again later.'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }

                    // Still navigate to summary screen even if save fails
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SummaryScreen(),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: const Text('Finish Shopping'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
