import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/cart.dart';
import '../models/store_theme.dart';
import '../providers/theme_provider.dart';
import 'dashboard_screen.dart';

class StoreSelectionScreen extends StatelessWidget {
  const StoreSelectionScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    // Get store themes from our StoreTheme model
    final stores = StoreTheme.storeThemes.entries
        .map((entry) => {
              'name': entry.key,
              'logo': entry.value.logo,
              'color': entry.value.primaryColor,
              'textColor': entry.value.textColor,
            })
        .toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Store'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 20),

            // Store icon
            Icon(
              Icons.storefront,
              size: 80,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 20),

            // Heading
            Text(
              'Where are you shopping today?',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 10),

            // Subheading
            Text(
              'Select a store to see their specials and branding',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 40),

            // Store options
            Expanded(
              child: ListView.builder(
                itemCount: stores.length,
                itemBuilder: (context, index) {
                  final store = stores[index];
                  final storeName = store['name'] as String;
                  final storeColor = store['color'] as Color;
                  final isDefault = storeName == 'Default';

                  return Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: InkWell(
                      onTap: () {
                        // Set the selected store in the cart provider
                        Provider.of<Cart>(context, listen: false)
                            .setStore(storeName);

                        // Navigate to the dashboard screen
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const DashboardScreen(),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        padding: const EdgeInsets.all(20.0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          gradient: isDefault
                              ? null
                              : LinearGradient(
                                  colors: [
                                    storeColor.withOpacity(0.7),
                                    storeColor
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                        ),
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: 30,
                              backgroundColor: isDefault
                                  ? Colors.white
                                  : storeColor.withOpacity(0.8),
                              child: Icon(
                                store['logo'] as IconData,
                                size: 30,
                                color: isDefault ? storeColor : Colors.white,
                              ),
                            ),
                            const SizedBox(width: 20),
                            Expanded(
                              child: Text(
                                storeName,
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: isDefault
                                      ? Colors.black
                                      : store['textColor'] as Color,
                                ),
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              color: isDefault ? Colors.black : Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
