import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class HelpSupportScreen extends StatelessWidget {
  const HelpSupportScreen({super.key});

  Future<void> _launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: 'subject=Budget Basket Support Request',
    );
    
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  Future<void> _launchPhone() async {
    final Uri phoneUri = Uri(scheme: 'tel', path: '+27123456789');
    
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  Future<void> _launchWhatsApp() async {
    final Uri whatsappUri = Uri.parse('https://wa.me/27123456789?text=Hi, I need help with Budget Basket app');
    
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Help & Support'),
        backgroundColor: theme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Contact Us Section
            Text(
              'Contact Us',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),

            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  ListTile(
                    leading: Icon(Icons.email, color: theme.primaryColor),
                    title: const Text('Email Support'),
                    subtitle: const Text('<EMAIL>'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: _launchEmail,
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: Icon(Icons.phone, color: theme.primaryColor),
                    title: const Text('Phone Support'),
                    subtitle: const Text('+27 12 345 6789'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: _launchPhone,
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: Icon(Icons.chat, color: Colors.green),
                    title: const Text('WhatsApp Support'),
                    subtitle: const Text('Quick chat support'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: _launchWhatsApp,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),

            // FAQ Section
            Text(
              'Frequently Asked Questions',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),

            _buildFAQItem(
              context,
              'How do I place an order?',
              'Browse products, add them to your cart, and proceed to checkout. You can pay using various methods including card or mobile money.',
            ),
            _buildFAQItem(
              context,
              'What are the delivery charges?',
              'Delivery charges vary by location. Free delivery is available for orders over R500 within selected areas.',
            ),
            _buildFAQItem(
              context,
              'How long does delivery take?',
              'Standard delivery takes 2-4 business days. Express delivery (1-2 days) is available for an additional fee.',
            ),
            _buildFAQItem(
              context,
              'Can I cancel my order?',
              'Orders can be cancelled within 1 hour of placement. After that, please contact support for assistance.',
            ),
            _buildFAQItem(
              context,
              'What payment methods do you accept?',
              'We accept credit/debit cards, EFT, mobile money, and cash on delivery in selected areas.',
            ),
            _buildFAQItem(
              context,
              'How do I track my order?',
              'You can track your order in the "Purchase History" section of your profile or through the tracking link sent via email.',
            ),
            const SizedBox(height: 32),

            // App Information
            Text(
              'App Information',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),

            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  ListTile(
                    leading: Icon(Icons.info, color: theme.primaryColor),
                    title: const Text('About Budget Basket'),
                    subtitle: const Text('Learn more about our app'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      _showAboutDialog(context);
                    },
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: Icon(Icons.privacy_tip, color: theme.primaryColor),
                    title: const Text('Privacy Policy'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      // Navigate to privacy policy
                    },
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: Icon(Icons.description, color: theme.primaryColor),
                    title: const Text('Terms of Service'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      // Navigate to terms of service
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),

            // Feedback Section
            Text(
              'Feedback',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),

            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: ListTile(
                leading: Icon(Icons.feedback, color: theme.primaryColor),
                title: const Text('Send Feedback'),
                subtitle: const Text('Help us improve the app'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  _showFeedbackDialog(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQItem(BuildContext context, String question, String answer) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ExpansionTile(
        title: Text(
          question,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              answer,
              style: TextStyle(color: Colors.grey[700]),
            ),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About Budget Basket'),
        content: const Text(
          'Budget Basket is your one-stop grocery shopping app. We help you find the best deals and prices across multiple stores, making grocery shopping convenient and affordable.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showFeedbackDialog(BuildContext context) {
    final feedbackController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Send Feedback'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('We value your feedback! Let us know how we can improve.'),
            const SizedBox(height: 16),
            TextField(
              controller: feedbackController,
              decoration: const InputDecoration(
                hintText: 'Enter your feedback here...',
                border: OutlineInputBorder(),
              ),
              maxLines: 4,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Send feedback
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Thank you for your feedback!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Send'),
          ),
        ],
      ),
    );
  }
}
