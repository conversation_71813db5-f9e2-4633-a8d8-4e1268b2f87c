import 'dart:async';
import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/cart.dart';
import '../models/product.dart';
import '../services/product_service.dart';
import '../widgets/cached_image_widget.dart';
import '../widgets/cart_badge.dart';
import 'cart_screen.dart';
import 'profile_screen.dart';
import 'manual_item_screen.dart';

class ScannerScreen extends StatefulWidget {
  const ScannerScreen({Key? key}) : super(key: key);

  @override
  State<ScannerScreen> createState() => _ScannerScreenState();
}

class _ScannerScreenState extends State<ScannerScreen> with WidgetsBindingObserver {
  final _barcodeController = TextEditingController();
  final _productService = ProductService();
  bool _isLoading = false;
  String _errorMessage = '';
  MobileScannerController? _scannerController;
  List<Product> _foundProducts = [];
  bool _showProductOverlay = false;
  // Map to store quantity for each product
  final Map<String, int> _productQuantities = {};
  // Last scanned product
  Product? _lastScannedProduct;
  // Flag to prevent multiple scans of the same barcode in quick succession
  String? _lastScannedBarcode;
  bool _scannerPaused = false;
  bool _hasCameraPermission = false;
  bool _hasInternetConnection = false;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    print("Scanner screen initialized");
    WidgetsBinding.instance.addObserver(this);
    
    // Initialize connectivity monitoring
    _initConnectivity();
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(_updateConnectionStatus);
    
    // Check camera permission on init
    _checkCameraPermission();
    
    // Initialize the scanner controller with a slight delay to ensure the widget is fully built
    Future.delayed(Duration(milliseconds: 300), () {
      if (mounted) {
        // Do nothing here as scanner initialization is handled in _checkCameraPermission
      }
    });
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Handle app lifecycle changes to properly manage camera resources
    print("App lifecycle state changed to: $state");
    
    if (state == AppLifecycleState.resumed) {
      // App is in the foreground
      if (_scannerController == null) {
        _initializeScanner();
      }
    } else if (state == AppLifecycleState.paused || 
               state == AppLifecycleState.inactive || 
               state == AppLifecycleState.detached) {
      // App is in the background or being closed
      _disposeScanner();
    }
  }

  void _initializeScanner() {
    try {
      print("Initializing scanner controller");
      
      // Create a new controller
      _scannerController = MobileScannerController(
        detectionSpeed: DetectionSpeed.normal,
        facing: CameraFacing.back,
        torchEnabled: false,
      );
      
      // Start the scanner
      _scannerController?.start().then((_) {
        print("Scanner started successfully");
        setState(() {
          _scannerPaused = false;
        });
      }).catchError((error) {
        print("Error starting scanner: $error");
        setState(() {
          _errorMessage = 'Failed to start camera: $error';
          _scannerPaused = true;
        });
      });
    } catch (e) {
      print("Error initializing scanner: $e");
      setState(() {
        _errorMessage = 'Camera error: $e';
        _scannerPaused = true;
      });
    }
  }
  
  void _disposeScanner() {
    print("Disposing scanner controller");
    _scannerController?.stop();
    _scannerController?.dispose();
    _scannerController = null;
  }

  @override
  void dispose() {
    print("Disposing scanner screen");
    WidgetsBinding.instance.removeObserver(this);
    _barcodeController.dispose();
    _disposeScanner();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  void _toggleScanner() {
    print("Toggling scanner. Current state: $_scannerPaused");
    
    if (!_scannerPaused) {
      // Pause the scanner
      _scannerController?.stop();
      setState(() {
        _scannerPaused = true;
      });
    } else {
      // Resume the scanner
      if (_scannerController == null) {
        _initializeScanner();
      } else {
        _scannerController?.start();
        setState(() {
          _scannerPaused = false;
        });
      }
    }
  }

  Future<void> _searchProduct(String barcode) async {
    print("Searching for product with barcode: $barcode");
    
    // Prevent multiple scans of the same barcode in quick succession
    if (_lastScannedBarcode == barcode && DateTime.now().difference(_lastScannedTime ?? DateTime.now()).inSeconds < 3) {
      print("Ignoring duplicate scan of barcode: $barcode");
      return;
    }
    
    _lastScannedBarcode = barcode;
    _lastScannedTime = DateTime.now();
    
    if (barcode.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter a barcode';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    // Pause the scanner while searching but don't stop it
    if (!_scannerPaused && _scannerController != null) {
      _scannerController?.stop();
    }

    try {
      final cart = Provider.of<Cart>(context, listen: false);
      final products = await _productService.searchByBarcode(
        barcode,
        cart.selectedStore,
      );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _foundProducts = products;
        _showProductOverlay = products.isNotEmpty;
        
        // Initialize quantities
        for (var product in products) {
          _productQuantities[product.id] = 1;
        }
        
        // Set the last scanned product
        if (products.isNotEmpty) {
          _lastScannedProduct = products.first;
        }
      });

      if (products.isEmpty) {
        print("No products found for barcode: $barcode");
        
        // Always resume the scanner first
        if (_scannerController != null) {
          _scannerController?.start().then((_) {
            setState(() {
              _scannerPaused = false;
            });
          });
        }
        
        // Show dialog asking if user wants to add manually
        if (mounted) {
          showDialog(
            context: context,
            builder: (ctx) => AlertDialog(
              title: const Text('Product Not Found'),
              content: const Text(
                'No product found for this barcode. Would you like to add this item manually?'
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(ctx).pop();
                  },
                  child: const Text('CONTINUE SCANNING'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(ctx).pop();
                    // Navigate to manual item screen with barcode and shop
                    final cart = Provider.of<Cart>(context, listen: false);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ManualItemScreen(
                          initialBarcode: barcode,
                          shop: cart.selectedStore,
                        ),
                      ),
                    ).then((_) {
                      // Make sure scanner is still running when returning
                      if (_scannerController == null) {
                        _initializeScanner();
                      } else {
                        _scannerController?.start().then((_) {
                          setState(() {
                            _scannerPaused = false;
                          });
                        });
                      }
                    });
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).primaryColor,
                  ),
                  child: const Text('ADD MANUALLY'),
                ),
              ],
            ),
          );
        }
        
        setState(() {
          _errorMessage = 'No products found for this barcode';
        });
        
        return;
      }
      
      print("Found ${products.length} products for barcode: $barcode");
    } catch (e) {
      print("Error searching for products: $e");
      if (!mounted) return;
      
      // Always resume the scanner first
      if (_scannerController != null) {
        _scannerController?.start().then((_) {
          setState(() {
            _scannerPaused = false;
          });
        });
      }
      
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error searching product: $e';
      });
    }
  }

  void _dismissProductOverlay() {
    print("Dismissing product overlay...");
    
    // Clear the overlay state
    setState(() {
      _showProductOverlay = false;
      _foundProducts = [];
      _productQuantities.clear();
    });
    
    // Resume scanning
    if (_scannerController != null) {
      _scannerController?.start().then((_) {
        setState(() {
          _scannerPaused = false;
        });
        print("Scanner restarted after dismissing overlay");
      }).catchError((error) {
        print("Error restarting scanner: $error");
        // If there's an error restarting, try to reinitialize
        _disposeScanner();
        _initializeScanner();
      });
    } else {
      // If controller is null, initialize it
      _initializeScanner();
    }
  }

  // Function to open product URL in browser
  Future<void> _openProductUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open product URL'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _incrementQuantity(String productId) {
    setState(() {
      _productQuantities[productId] = (_productQuantities[productId] ?? 1) + 1;
    });
  }

  void _decrementQuantity(String productId) {
    setState(() {
      final currentQty = _productQuantities[productId] ?? 1;
      if (currentQty > 1) {
        _productQuantities[productId] = currentQty - 1;
      }
    });
  }

  void _goToCart() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CartScreen(),
      ),
    ).then((_) {
      // When returning from cart, ensure scanner is active
      if (_scannerController == null) {
        _initializeScanner();
      } else {
        _scannerController?.start().then((_) {
          setState(() {
            _scannerPaused = false;
          });
        });
      }
    });
  }

  void _goToProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ProfileScreen(),
      ),
    ).then((_) {
      // When returning from profile, ensure scanner is active
      if (_scannerController == null) {
        _initializeScanner();
      } else {
        _scannerController?.start().then((_) {
          setState(() {
            _scannerPaused = false;
          });
        });
      }
    });
  }

  Future<void> _checkCameraPermission() async {
    final status = await Permission.camera.status;
    setState(() {
      _hasCameraPermission = status.isGranted;
    });
    
    if (status.isGranted) {
      // Initialize scanner only if we have permission
      Future.delayed(Duration(milliseconds: 300), () {
        if (mounted) {
          _initializeScanner();
        }
      });
    }
  }

  Future<void> _requestCameraPermission() async {
    final status = await Permission.camera.request();
    setState(() {
      _hasCameraPermission = status.isGranted;
    });
    
    if (status.isGranted) {
      _initializeScanner();
    }
  }

  Future<void> _initConnectivity() async {
    try {
      final result = await Connectivity().checkConnectivity();
      await _updateConnectionStatus(result);
    } catch (e) {
      print('Error checking connectivity: $e');
      setState(() {
        _hasInternetConnection = false;
      });
    }
  }

  Future<void> _updateConnectionStatus(ConnectivityResult result) async {
    setState(() {
      _hasInternetConnection = result != ConnectivityResult.none;
    });
    
    // If we lose internet connection while scanner is active, show the no internet dialog
    if (!_hasInternetConnection && _scannerController != null) {
      _showNoInternetDialog();
    }
  }

  void _showNoInternetDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (ctx) => AlertDialog(
        title: const Text('No Internet Connection'),
        content: const Text(
          'An internet connection is required to scan and search for products. Please check your connection and try again.'
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
              Navigator.of(context).pop(); // Return to previous screen
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan Product'),
        actions: [
          // Only show manual entry if we have internet
          if (_hasInternetConnection)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                _showManualBarcodeDialog();
              },
              tooltip: 'Enter barcode manually',
            ),
          IconButton(
            icon: Icon(_scannerPaused ? Icons.play_arrow : Icons.pause),
            onPressed: _toggleScanner,
            tooltip: _scannerPaused ? 'Resume scanner' : 'Pause scanner',
          ),
          CartBadge(
            child: IconButton(
              icon: const Icon(Icons.shopping_cart),
              onPressed: _goToCart,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: _goToProfile,
          ),
        ],
      ),
      body: Stack(
        children: [
          _buildScannerOrError(),
          
          // Only show product overlay if we have internet
          if (_showProductOverlay && _hasInternetConnection)
            _buildProductOverlay(),
            
          // Only show last scanned item if we have internet
          if (_lastScannedProduct != null && !_showProductOverlay && _hasInternetConnection)
            _buildLastScannedItemCard(),
        ],
      ),
    );
  }

  void _showManualBarcodeDialog() {
    // Don't show dialog if no internet
    if (!_hasInternetConnection) {
      _showNoInternetDialog();
      return;
    }

    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Enter Barcode'),
        content: TextField(
          controller: _barcodeController,
          decoration: const InputDecoration(
            labelText: 'Barcode',
            hintText: 'Enter product barcode',
          ),
          keyboardType: TextInputType.number,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
            },
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              final barcode = _barcodeController.text.trim();
              if (barcode.isNotEmpty) {
                Navigator.of(ctx).pop();
                _searchProduct(barcode);
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).primaryColor,
            ),
            child: const Text('SEARCH'),
          ),
        ],
      ),
    );
  }

  Widget _buildScannerOrError() {
    if (!_hasInternetConnection) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.wifi_off,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'No Internet Connection',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'An internet connection is required to scan and search for products',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () async {
                await _initConnectivity();
                if (!_hasInternetConnection) {
                  _showNoInternetDialog();
                }
              },
              child: const Text('Retry Connection'),
            ),
          ],
        ),
      );
    }

    if (!_hasCameraPermission) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.camera_alt,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'Camera Permission Required',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'We need camera access to scan product barcodes',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _requestCameraPermission,
              child: const Text('Grant Camera Permission'),
            ),
          ],
        ),
      );
    }

    if (_errorMessage.isNotEmpty && _foundProducts.isEmpty) {
      // Show error message but keep scanner running in background
      return Stack(
        children: [
          // Keep scanner running in background
          if (_scannerController != null)
            MobileScanner(
              controller: _scannerController!,
              onDetect: (capture) {
                final barcodes = capture.barcodes;
                if (barcodes.isNotEmpty && barcodes.first.rawValue != null) {
                  _searchProduct(barcodes.first.rawValue!);
                }
              },
            ),
          // Show error overlay
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  _errorMessage,
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _errorMessage = '';
                    });
                    // Make sure scanner is running
                    if (_scannerController != null && _scannerPaused) {
                      _scannerController?.start().then((_) {
                        setState(() {
                          _scannerPaused = false;
                        });
                      });
                    }
                  },
                  child: const Text('Continue Scanning'),
                ),
              ],
            ),
          ),
        ],
      );
    } else if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    
    if (_scannerController == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    
    return Stack(
      children: [
        // Scanner
        MobileScanner(
          controller: _scannerController,
          onDetect: (capture) {
            final List<Barcode> barcodes = capture.barcodes;
            if (barcodes.isNotEmpty && !_showProductOverlay) {
              final barcode = barcodes.first.rawValue ?? '';
              if (barcode.isNotEmpty) {
                print("Barcode detected: $barcode");
                _searchProduct(barcode);
              }
            }
          },
        ),
        
        // Scanner overlay
        if (!_showProductOverlay)
          Center(
            child: Container(
              width: 250,
              height: 250,
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.white,
                  width: 3,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          
        // Scan instructions
        if (!_showProductOverlay)
          Positioned(
            bottom: _lastScannedProduct != null ? 120 : 40,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  'Position barcode within the frame to scan',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          
        // Paused overlay
        if (_scannerPaused && !_showProductOverlay)
          Container(
            color: Colors.black.withOpacity(0.7),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.pause_circle_filled,
                    color: Colors.white,
                    size: 80,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Scanner Paused',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: _toggleScanner,
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('Resume Scanning'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildProductOverlay() {
    return Positioned.fill(
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Found ${_foundProducts.length} product(s)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: _foundProducts.length,
                itemBuilder: (context, index) {
                  final product = _foundProducts[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Product image
                              if (product.sanitizedImageUrl.isNotEmpty)
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: SizedBox(
                                    width: 100,
                                    height: 100,
                                    child: CachedImageWidget(
                                      imageUrl: product.sanitizedImageUrl,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              const SizedBox(width: 16),
                              // Product details
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      product.name,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      product.formattedPrice,
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                    if (product.brand.isNotEmpty) ...[
                                      const SizedBox(height: 8),
                                      Text(
                                        product.brand,
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey.shade700,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          // Quantity controls
                          Row(
                            children: [
                              const Text(
                                'Quantity:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(width: 16),
                              // Quantity selector
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  children: [
                                    // Decrement button
                                    IconButton(
                                      icon: const Icon(Icons.remove),
                                      onPressed: () => _decrementQuantity(product.id),
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(
                                        minWidth: 36,
                                        minHeight: 36,
                                      ),
                                    ),
                                    // Quantity display
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8),
                                      child: Text(
                                        '${_productQuantities[product.id] ?? 1}',
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    // Increment button
                                    IconButton(
                                      icon: const Icon(Icons.add),
                                      onPressed: () => _incrementQuantity(product.id),
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(
                                        minWidth: 36,
                                        minHeight: 36,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          // Add to cart button
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: () {
                                // Get the current quantity for this product
                                final quantity = _productQuantities[product.id] ?? 1;
                                
                                // Create a copy of the product with the updated quantity
                                final updatedProduct = product.copyWith(
                                  quantity: quantity,
                                );
                                
                                // Add to cart
                                final cart = Provider.of<Cart>(context, listen: false);
                                final bool success = cart.addProduct(updatedProduct);
                                
                                if (success) {
                                  // Show confirmation
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('${product.name} added to cart'),
                                      backgroundColor: Theme.of(context).primaryColor,
                                      duration: const Duration(seconds: 2),
                                    ),
                                  );
                                  
                                  // Close the product overlay
                                  _dismissProductOverlay();
                                } else {
                                  // Show error message
                                  String errorMessage = '';
                                  if (cart.hasReachedMaxItems) {
                                    errorMessage = 'Cannot add item. Maximum cart limit of ${Cart.maxItemCount} items reached.';
                                  } else if (cart.hasReachedMaxAmount) {
                                    errorMessage = 'Cannot add item. Maximum cart amount of R${Cart.maxTotalAmount.toStringAsFixed(2)} reached.';
                                  } else {
                                    errorMessage = 'Failed to add item to cart.';
                                  }
                                  
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(errorMessage),
                                      duration: const Duration(seconds: 3),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              },
                              icon: const Icon(Icons.shopping_cart),
                              label: const Text('Add to Trolley'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Theme.of(context).primaryColor,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                          // View details button
                          if (product.url.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: TextButton.icon(
                                onPressed: () => _openProductUrl(product.url),
                                icon: const Icon(Icons.open_in_new, size: 16),
                                label: const Text('View Details'),
                                style: TextButton.styleFrom(
                                  foregroundColor: Theme.of(context).primaryColor,
                                  padding: EdgeInsets.zero,
                                  alignment: Alignment.centerLeft,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 20),
            // Continue scanning button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _dismissProductOverlay,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Continue Scanning',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLastScannedItemCard() {
    if (_lastScannedProduct == null) return const SizedBox();
    
    final product = _lastScannedProduct!;
    
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Last Scanned Item',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                // Product image
                if (product.sanitizedImageUrl.isNotEmpty)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: SizedBox(
                      width: 60,
                      height: 60,
                      child: CachedImageWidget(
                        imageUrl: product.sanitizedImageUrl,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                const SizedBox(width: 12),
                // Product details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        product.formattedPrice,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                // Add to cart button
                IconButton(
                  icon: const Icon(Icons.add_shopping_cart),
                  color: Theme.of(context).primaryColor,
                  onPressed: () {
                    final cart = Provider.of<Cart>(context, listen: false);
                    final bool success = cart.addProduct(product);
                    
                    if (success) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('${product.name} added to cart'),
                          backgroundColor: Theme.of(context).primaryColor,
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    } else {
                      String errorMessage = '';
                      if (cart.hasReachedMaxItems) {
                        errorMessage = 'Cannot add item. Maximum cart limit reached.';
                      } else if (cart.hasReachedMaxAmount) {
                        errorMessage = 'Cannot add item. Maximum cart amount reached.';
                      } else {
                        errorMessage = 'Failed to add item to cart.';
                      }
                      
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(errorMessage),
                          duration: const Duration(seconds: 3),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  // Track the last scanned time to prevent duplicate scans
  DateTime? _lastScannedTime;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    
    // Ensure scanner is active when screen is shown
    if (mounted && _scannerController == null) {
      // Do nothing here as scanner initialization is handled in _checkCameraPermission
    } else if (mounted && _scannerPaused) {
      _scannerController?.start().then((_) {
        if (mounted) {
          setState(() {
            _scannerPaused = false;
          });
        }
      });
    }
  }
}
