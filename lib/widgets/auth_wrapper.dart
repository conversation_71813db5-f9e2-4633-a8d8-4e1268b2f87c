import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../screens/login_screen.dart';
import '../screens/dashboard_screen.dart';

/// A widget that redirects to either the login screen or the main app
/// based on the user's authentication state
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({Key? key}) : super(key: key);

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  Widget build(BuildContext context) {
    debugPrint('Building AuthWrapper');
    return Consumer<AuthService>(
      builder: (context, authService, _) {
        debugPrint(
            'AuthWrapper state - isLoading: ${authService.isLoading}, isLoggedIn: ${authService.isLoggedIn}');

        // Show loading indicator while checking auth state
        if (authService.isLoading) {
          debugPrint('AuthWrapper showing loading screen');
          return Scaffold(
            body: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.green.shade300,
                    Colors.green.shade700,
                  ],
                ),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.shopping_basket_rounded,
                      size: 100,
                      color: Colors.white,
                    ),
                    SizedBox(height: 24),
                    Text(
                      'Budget Basket',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Shop smart, stay within budget',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 48),
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        // Redirect based on authentication state
        final Widget nextScreen = authService.isLoggedIn
            ? const DashboardScreen()
            : const LoginScreen();

        debugPrint('AuthWrapper navigating to: ${nextScreen.runtimeType}');
        return nextScreen;
      },
    );
  }
}
