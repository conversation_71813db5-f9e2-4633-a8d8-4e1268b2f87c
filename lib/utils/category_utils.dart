import '../models/category.dart' as category_model;

class CategoryUtils {
  static String formatCategoryName(String category) {
    // Split by '/' and take the last part (most specific category)
    final parts = category.split('/');
    if (parts.isEmpty) return category;

    // Get the last part and decode URL encoding
    String lastPart = Uri.decodeComponent(parts.last);

    // Remove common prefixes and clean up
    lastPart = lastPart
        .replaceAll(
            RegExp(r'^[A-Za-z]+/'), '') // Remove leading category prefixes
        .replaceAll(
            RegExp(r'%[0-9A-Fa-f]{2}'), '') // Remove any remaining URL encoding
        .replaceAll('-', ' ') // Replace hyphens with spaces
        .replaceAll('_', ' '); // Replace underscores with spaces

    // Capitalize first letter of each word
    return lastPart.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  static String getCategoryBreadcrumb(String category) {
    final parts = category.split('/');
    if (parts.length <= 1) return '';

    // Take all parts except the last one and format them
    final breadcrumbParts = parts.take(parts.length - 1).map((part) {
      String formatted = Uri.decodeComponent(part);
      formatted = formatted
          .replaceAll('-', ' ')
          .replaceAll('_', ' ')
          .split(' ')
          .map((word) {
        if (word.isEmpty) return word;
        return word[0].toUpperCase() + word.substring(1).toLowerCase();
      }).join(' ');
      return formatted;
    }).toList();

    return breadcrumbParts.join(' · ');
  }

  static String getCategoryGroup(String category) {
    final parts = category.split('/');
    if (parts.length < 2) return 'Other';

    // Get the second part (index 1) and format it
    String groupName = Uri.decodeComponent(parts[1]);
    groupName = groupName
        .replaceAll('-', ' ')
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');

    return groupName;
  }

  static String getCategoryGroupIcon(String groupName) {
    final lowerGroup = groupName.toLowerCase();

    if (lowerGroup.contains('food')) {
      return '🍽️';
    } else if (lowerGroup.contains('drinks') ||
        lowerGroup.contains('beverages')) {
      return '🥤';
    } else if (lowerGroup.contains('household')) {
      return '🏠';
    } else if (lowerGroup.contains('toiletries') ||
        lowerGroup.contains('personal')) {
      return '🧴';
    } else if (lowerGroup.contains('baby')) {
      return '👶';
    } else if (lowerGroup.contains('pet')) {
      return '🐕';
    } else if (lowerGroup.contains('electronics')) {
      return '📱';
    } else if (lowerGroup.contains('toy')) {
      return '🧸';
    } else if (lowerGroup.contains('stationery')) {
      return '📝';
    } else if (lowerGroup.contains('sport')) {
      return '⚽';
    } else if (lowerGroup.contains('outdoor')) {
      return '🏕️';
    }

    return '🛒';
  }

  static String getCategoryIcon(String category) {
    final lowerCategory = category.toLowerCase();

    // Food categories
    if (lowerCategory.contains('drinks') ||
        lowerCategory.contains('beverages')) {
      return '🥤';
    } else if (lowerCategory.contains('wine')) {
      return '🍷';
    } else if (lowerCategory.contains('beer')) {
      return '🍺';
    } else if (lowerCategory.contains('coffee')) {
      return '☕';
    } else if (lowerCategory.contains('tea')) {
      return '🫖';
    } else if (lowerCategory.contains('juice')) {
      return '🧃';
    } else if (lowerCategory.contains('milk')) {
      return '🥛';
    } else if (lowerCategory.contains('yoghurt') ||
        lowerCategory.contains('yogurt')) {
      return '🥛';
    } else if (lowerCategory.contains('bread') ||
        lowerCategory.contains('bakery')) {
      return '🍞';
    } else if (lowerCategory.contains('fruit')) {
      return '🍎';
    } else if (lowerCategory.contains('vegetable')) {
      return '🥬';
    } else if (lowerCategory.contains('meat')) {
      return '🥩';
    } else if (lowerCategory.contains('chicken')) {
      return '🍗';
    } else if (lowerCategory.contains('fish') ||
        lowerCategory.contains('seafood')) {
      return '🐟';
    } else if (lowerCategory.contains('cheese')) {
      return '🧀';
    } else if (lowerCategory.contains('egg')) {
      return '🥚';
    } else if (lowerCategory.contains('rice')) {
      return '🍚';
    } else if (lowerCategory.contains('pasta')) {
      return '🍝';
    } else if (lowerCategory.contains('cereal')) {
      return '🥣';
    } else if (lowerCategory.contains('snack') ||
        lowerCategory.contains('chips')) {
      return '🍿';
    } else if (lowerCategory.contains('chocolate') ||
        lowerCategory.contains('sweet')) {
      return '🍫';
    } else if (lowerCategory.contains('ice cream') ||
        lowerCategory.contains('dessert')) {
      return '🍨';
    } else if (lowerCategory.contains('canned')) {
      return '🥫';
    } else if (lowerCategory.contains('frozen')) {
      return '🧊';
    } else if (lowerCategory.contains('spice') ||
        lowerCategory.contains('seasoning')) {
      return '🧂';
    } else if (lowerCategory.contains('oil')) {
      return '🫒';
    } else if (lowerCategory.contains('sauce') ||
        lowerCategory.contains('condiment')) {
      return '🍅';
    } else if (lowerCategory.contains('soup')) {
      return '🍲';
    } else if (lowerCategory.contains('baby')) {
      return '👶';
    } else if (lowerCategory.contains('pet')) {
      return '🐕';
    } else if (lowerCategory.contains('household') ||
        lowerCategory.contains('cleaning')) {
      return '🧽';
    } else if (lowerCategory.contains('toiletries') ||
        lowerCategory.contains('personal')) {
      return '🧴';
    } else if (lowerCategory.contains('medicine') ||
        lowerCategory.contains('health')) {
      return '💊';
    } else if (lowerCategory.contains('electronics')) {
      return '📱';
    } else if (lowerCategory.contains('toy')) {
      return '🧸';
    } else if (lowerCategory.contains('stationery')) {
      return '📝';
    } else if (lowerCategory.contains('clothing') ||
        lowerCategory.contains('fashion')) {
      return '👕';
    } else if (lowerCategory.contains('sport')) {
      return '⚽';
    } else if (lowerCategory.contains('outdoor') ||
        lowerCategory.contains('camping')) {
      return '🏕️';
    }

    // Default category icon
    return '🛒';
  }

  static String getCategoryImageUrl(String category) {
    final lowerCategory = category.toLowerCase();

    // Return relevant stock images based on category
    if (lowerCategory.contains('wine')) {
      return 'https://images.unsplash.com/photo-1510812431401-41d2bd2722f3?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('beer')) {
      return 'https://images.unsplash.com/photo-1535958636474-b021ee887b13?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('coffee')) {
      return 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('bread')) {
      return 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('fruit')) {
      return 'https://images.unsplash.com/photo-1619566636858-adf3ef46400b?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('vegetable')) {
      return 'https://images.unsplash.com/photo-1590779033100-9f60a05a013d?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('meat')) {
      return 'https://images.unsplash.com/photo-1607623814075-e51df1bdc82f?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('dairy') ||
        lowerCategory.contains('milk')) {
      return 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('snack')) {
      return 'https://images.unsplash.com/photo-1566478989037-eec170784d0b?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('chocolate')) {
      return 'https://images.unsplash.com/photo-1549007994-cb92b5b5b8b8?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('baby')) {
      return 'https://images.unsplash.com/photo-1544126592-807ade215a0b?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('household')) {
      return 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('toiletries')) {
      return 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('electronics')) {
      return 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=300&fit=crop';
    } else if (lowerCategory.contains('toy')) {
      return 'https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?w=400&h=300&fit=crop';
    }

    // Default grocery image
    return 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=400&h=300&fit=crop';
  }
}

class CategoryGroup {
  final String name;
  final String icon;
  final List<category_model.Category> categories;
  final int totalProductCount;

  CategoryGroup({
    required this.name,
    required this.icon,
    required this.categories,
  }) : totalProductCount =
            categories.fold(0, (sum, category) => sum + category.productCount);

  static List<CategoryGroup> groupCategories(
      List<category_model.Category> categories) {
    final Map<String, List<category_model.Category>> groupedMap = {};

    for (final category in categories) {
      final groupName = CategoryUtils.getCategoryGroup(category.category);
      if (!groupedMap.containsKey(groupName)) {
        groupedMap[groupName] = [];
      }
      groupedMap[groupName]!.add(category);
    }

    // Convert to CategoryGroup objects and sort by total product count
    final groups = groupedMap.entries.map((entry) {
      return CategoryGroup(
        name: entry.key,
        icon: CategoryUtils.getCategoryGroupIcon(entry.key),
        categories: entry.value,
      );
    }).toList();

    // Sort by total product count (descending)
    groups.sort((a, b) => b.totalProductCount.compareTo(a.totalProductCount));

    return groups;
  }
}
