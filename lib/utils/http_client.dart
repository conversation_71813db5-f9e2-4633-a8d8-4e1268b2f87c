import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../services/secure_storage_service.dart';

class HttpClient {
  static const String baseUrl = 'https://www.kemavuso.co.za/budget';

  // Get the Firebase JWT token from secure storage
  static Future<String?> getJwtToken() async {
    try {
      final token = await SecureStorageService.getFirebaseJwtToken();
      
      if (token == null || token.isEmpty) {
        debugPrint('No JWT token found in secure storage');
        return null;
      }
      
      debugPrint('JWT token found in secure storage');
      return token;
    } catch (e) {
      debugPrint('Error getting JWT token from secure storage: $e');
      return null;
    }
  }

  // Add JWT token to headers
  static Future<Map<String, String>> getHeaders() async {
    final headers = {
      'Content-Type': 'application/json',
    };

    final token = await getJwtToken();
    if (token != null && token.isNotEmpty) {
      headers['Authorization'] = 'Bearer $token';
      debugPrint('Added Authorization header');
    } else {
      debugPrint('Warning: No Authorization header added - token not found');
    }

    return headers;
  }

  // Handle HTTP response
  static Future<http.Response> _handleResponse(http.Response response) async {
    debugPrint('Response status code: ${response.statusCode}');
    
    if (response.statusCode == 401) {
      debugPrint('Unauthorized request - token might be expired');
      // You might want to trigger a token refresh here
    }
    
    return response;
  }

  // GET request with JWT token
  static Future<http.Response> get(String endpoint) async {
    final headers = await getHeaders();
    final url = Uri.parse('$baseUrl$endpoint');
    
    debugPrint('GET request to: $url');
    debugPrint('Headers: $headers');
    
    final response = await http.get(url, headers: headers);
    return _handleResponse(response);
  }

  // POST request with JWT token
  static Future<http.Response> post(String endpoint, dynamic body) async {
    final headers = await getHeaders();
    final url = Uri.parse('$baseUrl$endpoint');
    
    debugPrint('POST request to: $url');
    debugPrint('Headers: $headers');
    debugPrint('Body: ${jsonEncode(body)}');
    
    final response = await http.post(url, headers: headers, body: jsonEncode(body));
    return _handleResponse(response);
  }

  // PUT request with JWT token
  static Future<http.Response> put(String endpoint, dynamic body) async {
    final headers = await getHeaders();
    final url = Uri.parse('$baseUrl$endpoint');
    
    debugPrint('PUT request to: $url');
    debugPrint('Headers: $headers');
    debugPrint('Body: ${jsonEncode(body)}');
    
    final response = await http.put(url, headers: headers, body: jsonEncode(body));
    return _handleResponse(response);
  }

  // DELETE request with JWT token
  static Future<http.Response> delete(String endpoint) async {
    final headers = await getHeaders();
    final url = Uri.parse('$baseUrl$endpoint');
    
    debugPrint('DELETE request to: $url');
    debugPrint('Headers: $headers');
    
    final response = await http.delete(url, headers: headers);
    return _handleResponse(response);
  }
}
