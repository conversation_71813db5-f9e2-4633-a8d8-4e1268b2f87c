import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'services/preferences_service.dart';
import 'widgets/auth_wrapper.dart';
import 'providers/theme_provider.dart';
import 'services/deep_link_service.dart';
import 'firebase_options.dart';
import 'models/cart.dart';
import 'services/auth_service.dart';
import 'services/user_service.dart';
import 'services/permission_service.dart';
import 'services/payment_service.dart';
import 'widgets/privacy_disclaimer_dialog.dart';
import 'services/admob_service.dart';
import 'services/order_service.dart';
import 'services/notification_service.dart';
import 'services/migration_service.dart';
import 'package:oktoast/oktoast.dart';
import 'screens/search_screen.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  try {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }
  } catch (e) {
    debugPrint('Firebase initialization error: $e');
    // Continue without Firebase for now
  }

  // Initialize AdMob with proper configuration
  await AdMobService.initGoogleMobileAds();

  // Initialize deep linking
  await DeepLinkService.initDeepLinks();

  // Migrate data from SharedPreferences to secure storage
  await MigrationService.migrateToSecureStorage();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => AuthService()),
        ChangeNotifierProvider(create: (_) => UserService()),
        ChangeNotifierProvider(create: (_) => PermissionService()),
        ChangeNotifierProvider(create: (_) => PaymentService()),
        ChangeNotifierProvider(create: (_) => Cart()),
        ChangeNotifierProvider(create: (_) => OrderService()),
        ChangeNotifierProvider(create: (_) => NotificationService()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return OKToast(
            child: MaterialApp(
              title: 'Budget Basket',
              theme: themeProvider.lightTheme,
              darkTheme: themeProvider.darkTheme,
              themeMode:
                  themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
              debugShowCheckedModeBanner: false,
              home: const SplashScreen(),
              onGenerateRoute: (settings) {
                // Handle named routes
                switch (settings.name) {
                  case '/search':
                    return MaterialPageRoute(
                        builder: (context) => const SearchScreen());
                  default:
                    // Handle deep link routes
                    final uri = settings.name != null
                        ? Uri.tryParse(settings.name!)
                        : null;
                    if (uri != null) {
                      DeepLinkService.handleIncomingLink(uri);
                    }
                    return null;
                }
              },
            ),
          );
        },
      ),
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkFirstLaunch();
  }

  Future<void> _checkFirstLaunch() async {
    try {
      // Add a small delay to ensure Navigator is ready
      await Future.delayed(const Duration(milliseconds: 500));

      bool hasShownPrivacy = await PreferencesService.hasAcceptedPrivacy();

      if (!hasShownPrivacy) {
        if (!mounted) return;

        // Show the unified privacy and permissions dialog
        bool? accepted = await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => PrivacyDisclaimerDialog(
            onAccept: () {
              Navigator.of(context).pop(true);
            },
          ),
        );

        if (accepted != true) {
          SystemNavigator.pop(); // Exit the app if user doesn't accept
          return;
        }

        await PreferencesService.setPrivacyAccepted(true);
      }

      // Add a small delay to ensure any previous navigation is complete
      await Future.delayed(const Duration(milliseconds: 100));

      if (!mounted) return;

      // Use WidgetsBinding.instance to ensure we're not in the middle of a frame
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const AuthWrapper()),
          );
        }
      });
    } catch (e) {
      debugPrint('Navigation error in splash screen: $e');
      // Attempt fallback navigation if the first attempt fails, with a delay
      await Future.delayed(const Duration(milliseconds: 300));
      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const AuthWrapper()),
            );
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF4CAF50), // Primary green
              Color(0xFF2E7D32), // Darker green
            ],
          ),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.shopping_basket_rounded,
                size: 100,
                color: Colors.white,
              ),
              SizedBox(height: 24),
              Text(
                'Budget Basket',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontFamily: 'system',
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Shop smart, stay within budget',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                  fontFamily: 'system',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
