{"app_name": "Grocery Store", "design_system": {"primary_color": "#4CAF50", "secondary_color": "#81C784", "accent_color": "#FFA726", "background_colors": {"primary": "#FFFFFF", "secondary": "#F5F5F5", "dark_mode": "#1E1E1E", "overlay": "rgba(0,0,0,0.5)"}, "text_colors": {"primary": "#212121", "secondary": "#757575", "light": "#FFFFFF", "accent": "#4CAF50"}, "typography": {"heading_large": {"size": "24px", "weight": "bold", "family": "system"}, "heading_medium": {"size": "20px", "weight": "semibold", "family": "system"}, "body_regular": {"size": "16px", "weight": "normal", "family": "system"}, "body_small": {"size": "14px", "weight": "normal", "family": "system"}, "caption": {"size": "12px", "weight": "normal", "family": "system"}}}, "screen_types": [{"name": "splash_screen", "description": "App launch screen with logo and branding", "layout": {"type": "centered", "background": "gradient_green", "elements": [{"type": "logo", "position": "center", "style": "white_icon_with_text"}, {"type": "app_name", "text": "Grocery Store", "position": "below_logo", "color": "white", "size": "large"}]}}, {"name": "welcome_screen", "description": "Onboarding screen with hero image and CTA", "layout": {"type": "vertical_stack", "padding": "16px", "elements": [{"type": "hero_illustration", "position": "top_center", "style": "colorful_shopping_cart"}, {"type": "heading", "text": "Welcome to our store", "alignment": "center", "size": "large"}, {"type": "subtitle", "text": "Get your groceries in as fast as one hour", "alignment": "center", "color": "secondary"}, {"type": "primary_button", "text": "Get Started", "width": "full", "position": "bottom"}]}}, {"name": "category_grid", "description": "Main store categories in grid layout", "layout": {"type": "grid_2x3", "padding": "16px", "elements": [{"type": "category_card", "structure": {"background": "white", "border_radius": "12px", "shadow": "light", "content": [{"type": "icon", "position": "top_left", "style": "colored_category_icon"}, {"type": "title", "position": "bottom", "size": "medium"}]}, "variants": ["vegetables", "fruits", "meat_fish", "dairy_eggs", "beverages", "snacks"]}]}}, {"name": "product_listing", "description": "Grid of products with images and prices", "layout": {"type": "scrollable_grid", "columns": 2, "padding": "16px", "header": {"type": "search_bar", "placeholder": "Search products"}, "elements": [{"type": "product_card", "structure": {"background": "white", "border_radius": "12px", "shadow": "light", "content": [{"type": "product_image", "position": "top", "aspect_ratio": "1:1", "background": "light_gray"}, {"type": "product_name", "position": "below_image", "size": "medium", "lines": 2}, {"type": "price", "position": "bottom_left", "color": "primary", "weight": "bold"}, {"type": "add_button", "position": "bottom_right", "style": "green_circular"}]}}]}}, {"name": "product_details", "description": "Individual product page with full details", "layout": {"type": "scrollable_vertical", "elements": [{"type": "product_image", "position": "top", "height": "300px", "background": "light_gray"}, {"type": "content_section", "padding": "16px", "elements": [{"type": "product_title", "size": "large", "weight": "bold"}, {"type": "price", "size": "large", "color": "primary", "weight": "bold"}, {"type": "description", "size": "regular", "color": "secondary"}, {"type": "quantity_selector", "style": "horizontal_stepper"}, {"type": "add_to_cart_button", "width": "full", "style": "primary_green"}]}]}}, {"name": "shopping_cart", "description": "Cart items list with totals", "layout": {"type": "list_with_footer", "header": {"type": "page_title", "text": "Shopping Cart"}, "content": {"type": "scrollable_list", "item_structure": {"type": "cart_item", "layout": "horizontal", "elements": [{"type": "product_image", "size": "60px", "position": "left"}, {"type": "product_info", "position": "center", "elements": ["product_name", "unit_price"]}, {"type": "quantity_controls", "position": "right", "style": "stepper"}]}}, "footer": {"type": "checkout_summary", "background": "white", "shadow": "top", "elements": [{"type": "total_price", "size": "large", "weight": "bold"}, {"type": "checkout_button", "style": "primary_full_width"}]}}}, {"name": "checkout_form", "description": "Address and payment information forms", "layout": {"type": "form_layout", "sections": [{"title": "Delivery Address", "fields": [{"type": "text_input", "label": "Full Name", "required": true}, {"type": "text_input", "label": "Address", "required": true}, {"type": "text_input", "label": "City", "required": true}]}, {"title": "Payment Method", "fields": [{"type": "radio_group", "options": ["Credit Card", "Debit Card", "Cash on Delivery"]}]}], "footer": {"type": "submit_button", "text": "Place Order", "style": "primary_full_width"}}}, {"name": "profile_menu", "description": "User profile and settings menu", "layout": {"type": "list_menu", "header": {"type": "user_profile", "elements": [{"type": "avatar", "style": "circular", "size": "large"}, {"type": "user_name", "size": "large"}, {"type": "user_email", "size": "small", "color": "secondary"}]}, "menu_items": [{"type": "menu_item", "structure": {"icon": "left", "text": "center", "arrow": "right"}, "items": ["My Orders", "Addresses", "Payment Methods", "Notifications", "Help & Support", "About"]}]}}], "component_patterns": {"buttons": {"primary": {"background": "#4CAF50", "text_color": "white", "border_radius": "8px", "padding": "12px 24px", "font_weight": "semibold"}, "secondary": {"background": "transparent", "text_color": "#4CAF50", "border": "1px solid #4CAF50", "border_radius": "8px", "padding": "12px 24px"}, "floating_add": {"background": "#4CAF50", "shape": "circular", "size": "40px", "icon": "plus", "shadow": "medium"}}, "cards": {"product_card": {"background": "white", "border_radius": "12px", "shadow": "0 2px 8px rgba(0,0,0,0.1)", "padding": "12px", "aspect_ratio": "3:4"}, "category_card": {"background": "white", "border_radius": "12px", "shadow": "0 2px 8px rgba(0,0,0,0.1)", "padding": "16px", "aspect_ratio": "1:1"}}, "navigation": {"bottom_tab_bar": {"background": "white", "height": "80px", "shadow": "top", "tabs": [{"icon": "home", "label": "Home"}, {"icon": "search", "label": "Search"}, {"icon": "shopping_cart", "label": "<PERSON><PERSON>"}, {"icon": "user", "label": "Profile"}]}, "header": {"height": "56px", "background": "white", "elements": [{"type": "back_button", "position": "left"}, {"type": "title", "position": "center"}, {"type": "action_button", "position": "right"}]}}, "inputs": {"search_bar": {"background": "#F5F5F5", "border_radius": "24px", "padding": "12px 16px", "placeholder_color": "#9E9E9E", "icon": "search_left"}, "text_field": {"border": "1px solid #E0E0E0", "border_radius": "8px", "padding": "12px 16px", "focus_border_color": "#4CAF50"}}}, "layout_principles": {"spacing": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px"}, "grid_system": {"columns": 12, "gutter": "16px", "margin": "16px"}, "responsive_breakpoints": {"mobile": "375px", "tablet": "768px"}}, "interaction_patterns": {"loading_states": {"skeleton_loading": true, "spinner_color": "#4CAF50"}, "gestures": {"pull_to_refresh": true, "swipe_to_delete": true, "tap_to_select": true}, "animations": {"page_transitions": "slide", "button_press": "scale", "loading": "fade"}}, "dark_mode_support": {"enabled": true, "background_colors": {"primary": "#121212", "secondary": "#1E1E1E", "surface": "#2D2D2D"}, "text_colors": {"primary": "#FFFFFF", "secondary": "#B3B3B3"}}}