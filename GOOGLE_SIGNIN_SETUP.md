# Google Sign-In Setup Guide

This guide will help you fix the Google Sign-In error (Error Code 10) in the Budget Basket app.

## The Problem

The error message `Failed to sign in with google: platformException(sign_in_failed) gms common exception 10` occurs because the OAuth configuration is missing in your Firebase project. This is evident in your `google-services.json` file, where the `oauth_client` array is empty:

```json
"oauth_client": [],
```

## How to Fix It

Follow these steps to properly configure Google Sign-In for your app:

### 1. Enable Google Sign-In in Firebase Console

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project (Budget Basket)
3. Go to **Authentication** in the left sidebar
4. Click on the **Sign-in method** tab
5. Enable **Google** as a sign-in provider
6. Add your support email
7. Save the changes

### 2. Configure OAuth Consent Screen

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project (same one linked to Firebase)
3. Navigate to **APIs & Services** > **OAuth consent screen**
4. Choose **External** user type (unless you have a Google Workspace)
5. Fill in the required information:
   - App name: Budget Basket
   - User support email
   - Developer contact information
6. Click **Save and Continue**
7. Add scopes (at minimum, select `email` and `profile`)
8. Click **Save and Continue**
9. Add test users (your email)
10. Click **Save and Continue**

### 3. Create OAuth Client ID

1. In the Google Cloud Console, go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth client ID**
3. Choose **Android** as the application type
4. Enter a name (e.g., "Budget Basket Android")
5. Enter your package name: `za.co.budgetbasket`
6. Generate and enter your SHA-1 certificate fingerprint:
   ```bash
   cd android && ./gradlew signingReport
   ```
7. Click **Create**

### 4. Download Updated google-services.json

1. Go back to the Firebase Console
2. Go to Project Settings (gear icon)
3. In the **Your apps** section, download a new `google-services.json` file
4. Replace the existing file in your project at `android/app/google-services.json`

### 5. Rebuild Your App

```bash
flutter clean
flutter pub get
flutter run
```

## Temporary Workaround

Until you complete the above steps, the app will use anonymous sign-in as a fallback. This allows you to continue using the app as a guest, but with limited functionality.

## Need Help?

If you continue to experience issues after following these steps, check the following:

1. Verify that your SHA-1 fingerprint is correct
2. Ensure that the Google Sign-In API is enabled in the Google Cloud Console
3. Check that your Firebase project and Google Cloud project are properly linked
4. Verify that your app's package name matches exactly in all configurations
