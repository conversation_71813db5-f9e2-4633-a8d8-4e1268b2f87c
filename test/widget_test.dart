// This is a basic Flutter widget test for Budget Basket app.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:budget_basket/models/cart.dart';
import 'package:budget_basket/screens/dashboard_screen.dart';
import 'package:budget_basket/screens/product_details_screen.dart';
import 'package:budget_basket/models/grocery_product.dart';

void main() {
  testWidgets('Dashboard screen smoke test', (WidgetTester tester) async {
    // Build the dashboard screen directly with required providers
    await tester.pumpWidget(
      MaterialApp(
        home: ChangeNotifierProvider(
          create: (context) => Cart(),
          child: const DashboardScreen(),
        ),
      ),
    );

    // Wait for the initial frame and network requests to complete
    await tester.pump();
    await tester.pump(const Duration(seconds: 1));

    // Verify that the app loads with the location header
    expect(find.text('Location'), findsOneWidget);
    expect(find.text('2972 Westheimer Rd.'), findsOneWidget);

    // Verify that the search bar is present
    expect(find.text('Search...'), findsOneWidget);

    // Verify that the Categories section is present
    expect(find.text('Categories'), findsOneWidget);

    // Verify that promotional banners are present
    expect(find.text('Savon Stories'), findsOneWidget);
    expect(find.text('Fresh'), findsOneWidget);
  });

  testWidgets('Product details screen test', (WidgetTester tester) async {
    // Create a test product
    final testProduct = GroceryProduct(
      id: 'test-1',
      name: 'Test Product',
      shopName: 'Test Shop',
      description: 'A test product for testing',
      price: 10.99,
      discount: 0.1,
      isOnPromotion: true,
      images: ['https://example.com/test-image.jpg'],
      promotions: [],
      displayCategories: ['Test Category'],
    );

    // Build the product details screen
    await tester.pumpWidget(
      MaterialApp(
        home: ChangeNotifierProvider(
          create: (context) => Cart(),
          child: ProductDetailsScreen(product: testProduct),
        ),
      ),
    );

    // Wait for the initial frame
    await tester.pump();

    // Verify that the product details are displayed
    expect(find.text('Available at Test Shop'), findsOneWidget);
    expect(find.text('A test product for testing'), findsOneWidget);

    // Verify that the add to cart button is present
    expect(find.text('Add to Cart - R9.89'), findsOneWidget);

    // Verify that the product name appears in the app bar (not counting duplicates in body)
    expect(find.byType(AppBar), findsOneWidget);
  });

  testWidgets('Cart functionality test', (WidgetTester tester) async {
    // Create a cart instance
    final cart = Cart();

    // Test cart is initially empty
    expect(cart.items.length, 0);
    expect(cart.totalPrice, 0.0);

    // This test doesn't require UI, just testing the cart model
  });
}
